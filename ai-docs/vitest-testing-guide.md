# Vitest 测试环境使用指南

## 概述

本文档介绍了为 client 前端项目配置的 Vitest 测试环境，包括环境配置、测试编写、运行方式和最佳实践。

## 技术栈

- **测试框架**: Vitest
- **DOM 环境**: jsdom
- **React 测试**: @testing-library/react
- **用户交互**: @testing-library/user-event
- **断言扩展**: @testing-library/jest-dom
- **覆盖率**: @vitest/coverage-v8

## 项目结构

```
client/
├── vitest.config.ts          # Vitest 配置文件
├── src/
│   ├── test/
│   │   ├── setup.ts          # 测试环境设置
│   │   └── examples/         # 测试示例
│   ├── utils/
│   │   └── __tests__/        # 工具函数测试
│   └── components/
│       └── **/__tests__/     # 组件测试
└── coverage/                 # 覆盖率报告输出目录
```

## 配置说明

### vitest.config.ts

主要配置项：

- **environment**: 'jsdom' - 提供浏览器 DOM 环境
- **globals**: true - 全局可用测试函数（describe, it, expect 等）
- **setupFiles**: 测试环境初始化文件
- **include/exclude**: 测试文件匹配规则
- **coverage**: 覆盖率配置
- **resolve.alias**: 路径别名配置（@/ 指向 src/）

### setup.ts

测试环境初始化，包含：

- DOM API Mock（matchMedia, ResizeObserver 等）
- 浏览器 API Mock（localStorage, sessionStorage 等）
- UmiJS 相关 Mock（history, useModel 等）
- Ant Design 组件 Mock（message 等）
- 时区设置和全局变量配置

## 测试命令

```bash
# 运行所有测试
npm run test

# 运行测试（一次性）
npm run test:run

# 监听模式运行测试
npm run test:watch

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试并生成覆盖率
npm run test:coverage:watch

# 使用 UI 界面运行测试
npm run test:ui
```

## 测试编写指南

### 1. 工具函数测试

```typescript
// src/utils/__tests__/index.test.ts
import { describe, it, expect } from 'vitest';
import { formatTime, isValidBundle } from '../index';

describe('Utils Functions', () => {
  describe('formatTime', () => {
    it('should format date to UTC time string', () => {
      const date = new Date('2023-12-25T10:30:45.000Z');
      const result = formatTime(date);
      expect(result).toBe('2023-12-25 10:30:45');
    });
  });

  describe('isValidBundle', () => {
    it('should validate iOS bundle', () => {
      expect(isValidBundle('123456789')).toBe(true);
    });

    it('should validate Android bundle', () => {
      expect(isValidBundle('com.example.app')).toBe(true);
    });
  });
});
```

### 2. React 组件测试

```typescript
// src/components/Button/EditButton/__tests__/index.test.tsx
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import EditButton from '../index';

// Mock 依赖
vi.mock('@/components/RixEngineFont', () => ({
  default: ({ type }: { type: string }) => <span data-testid="rix-icon">{type}</span>,
}));

describe('EditButton', () => {
  it('should render with default icon', () => {
    render(<EditButton />);
    
    const button = screen.getByRole('button');
    const icon = screen.getByTestId('rix-icon');
    
    expect(button).toBeInTheDocument();
    expect(icon).toHaveTextContent('rix-edit');
  });

  it('should handle click events', () => {
    const handleClick = vi.fn();
    render(<EditButton onClick={handleClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### 3. 表单组件测试

```typescript
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

describe('SearchForm', () => {
  it('should submit form with valid data', async () => {
    const mockOnSearch = vi.fn();
    const user = userEvent.setup();
    
    render(<SearchForm onSearch={mockOnSearch} />);
    
    await user.type(screen.getByLabelText('关键词'), 'test');
    await user.click(screen.getByRole('button', { name: '搜索' }));
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({ keyword: 'test' });
    });
  });
});
```

### 4. API 请求测试

```typescript
import { vi } from 'vitest';
import { fetchData } from '@/utils/index';

describe('API Tests', () => {
  it('should handle successful response', async () => {
    const mockRequest = vi.fn().mockResolvedValue({
      code: 0,
      data: { id: 1, name: 'Test' }
    });
    
    const mockOnSuccess = vi.fn();
    
    await fetchData({
      request: mockRequest,
      params: { id: 1 },
      onSuccess: mockOnSuccess,
    });
    
    expect(mockOnSuccess).toHaveBeenCalledWith({ id: 1, name: 'Test' });
  });
});
```

## Mock 策略

### 1. 组件 Mock

```typescript
// Mock 外部组件
vi.mock('@/components/RixEngineFont', () => ({
  default: ({ type }: { type: string }) => <span>{type}</span>,
}));

// Mock CSS 模块
vi.mock('./index.less', () => ({
  'edit-button': 'mock-class',
}));
```

### 2. API Mock

```typescript
// Mock 整个模块
vi.mock('@/services/api', () => ({
  getUserList: vi.fn(),
  createUser: vi.fn(),
}));

// Mock 特定函数
const mockRequest = vi.fn();
```

### 3. UmiJS Mock

```typescript
// 已在 setup.ts 中全局配置
vi.mock('umi', () => ({
  history: {
    push: vi.fn(),
    location: { pathname: '/' },
  },
  useModel: vi.fn(),
}));
```

## 最佳实践

### 1. 测试文件组织

- 工具函数测试：`src/utils/__tests__/`
- 组件测试：`src/components/**/__tests__/`
- 页面测试：`src/pages/**/__tests__/`
- 服务测试：`src/services/**/__tests__/`

### 2. 测试命名

- 描述性测试名称：`should render with correct props`
- 分组相关测试：使用 `describe` 块
- 测试文件命名：`*.test.ts` 或 `*.spec.ts`

### 3. 断言建议

```typescript
// 推荐：具体的断言
expect(button).toHaveClass('ant-btn-primary');
expect(input).toHaveValue('expected value');
expect(mockFn).toHaveBeenCalledWith(expectedArgs);

// 避免：过于宽泛的断言
expect(component).toBeTruthy();
```

### 4. 异步测试

```typescript
// 使用 waitFor 等待异步操作
await waitFor(() => {
  expect(screen.getByText('Success')).toBeInTheDocument();
});

// 使用 user-event 模拟用户交互
const user = userEvent.setup();
await user.click(button);
await user.type(input, 'text');
```

### 5. 清理和重置

```typescript
import { beforeEach, afterEach } from 'vitest';

beforeEach(() => {
  vi.clearAllMocks();
});

afterEach(() => {
  vi.restoreAllMocks();
});
```

## 覆盖率要求

当前配置的覆盖率阈值：

- 分支覆盖率：50%
- 函数覆盖率：50%
- 行覆盖率：50%
- 语句覆盖率：50%

可在 `vitest.config.ts` 中调整阈值。

## 常见问题

### 1. 依赖冲突

如果遇到依赖版本冲突，可以使用：
```bash
npm install --legacy-peer-deps
```

### 2. Mock 不生效

确保 Mock 在导入被测试模块之前声明：
```typescript
vi.mock('./module'); // 必须在 import 之前
import { Component } from './component';
```

### 3. 异步测试超时

增加测试超时时间：
```typescript
it('async test', async () => {
  // test code
}, 10000); // 10 秒超时
```

## 示例文件位置

- 工具函数测试：`src/utils/__tests__/index.test.ts`
- 组件测试：`src/components/Button/EditButton/__tests__/index.test.tsx`
- 表单测试：`src/test/examples/SearchForm.test.tsx`
- API 测试：`src/test/examples/api.test.ts`

## 依赖安装

如果需要重新安装测试相关依赖，请运行：

```bash
cd client
npm install --save-dev --legacy-peer-deps vitest @vitest/coverage-v8 jsdom @testing-library/react@^12.1.5 @testing-library/jest-dom @testing-library/user-event@^14.5.2
```

注意：由于项目使用 React 17，需要安装兼容版本的 testing-library。

## IDE 配置

### VSCode 配置

推荐安装以下扩展：

- Vitest (ZixuanChen.vitest-explorer)
- Jest (Orta.vscode-jest)

在 `.vscode/settings.json` 中添加：

```json
{
  "vitest.enable": true,
  "vitest.commandLine": "npm run test",
  "typescript.preferences.includePackageJsonAutoImports": "on"
}
```

### TypeScript 配置

确保 `tsconfig.json` 包含测试类型：

```json
{
  "compilerOptions": {
    "types": ["vitest/globals", "@testing-library/jest-dom"]
  }
}
```

## 调试测试

### 1. 使用 console.log

```typescript
it('debug test', () => {
  const result = myFunction();
  console.log('Debug result:', result);
  expect(result).toBe(expected);
});
```

### 2. 使用 screen.debug()

```typescript
it('debug component', () => {
  render(<MyComponent />);
  screen.debug(); // 打印当前 DOM 结构
});
```

### 3. VSCode 调试

在 `.vscode/launch.json` 中添加：

```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Vitest",
  "program": "${workspaceFolder}/client/node_modules/vitest/vitest.mjs",
  "args": ["run", "--reporter=verbose"],
  "cwd": "${workspaceFolder}/client",
  "console": "integratedTerminal"
}
```

## 性能优化

### 1. 并行测试

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1,
      }
    }
  }
});
```

### 2. 测试隔离

```typescript
// 避免测试间相互影响
beforeEach(() => {
  vi.clearAllMocks();
  cleanup(); // 清理 DOM
});
```

### 3. 选择性运行

```bash
# 运行特定文件
npm run test -- src/utils/__tests__/index.test.ts

# 运行匹配模式的测试
npm run test -- --grep "formatTime"

# 跳过某些测试
npm run test -- --exclude "**/slow.test.ts"
```

## 扩展阅读

- [Vitest 官方文档](https://vitest.dev/)
- [Testing Library 文档](https://testing-library.com/)
- [Jest DOM 匹配器](https://github.com/testing-library/jest-dom)
- [User Event 文档](https://testing-library.com/docs/user-event/intro)
- [React Testing 最佳实践](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
