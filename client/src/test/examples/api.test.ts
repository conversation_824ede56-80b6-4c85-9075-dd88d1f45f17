import { fetchData } from '@/utils/index';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock axios or request library
const mockRequest = vi.fn();

describe('API Request Tests', () => {
  const mockSetLoading = vi.fn();
  const mockOnSuccess = vi.fn();
  const mockOnError = vi.fn();
  const mockOnFinally = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('fetchData', () => {
    it('should handle successful API response', async () => {
      const mockResponse = {
        code: 0,
        data: { id: 1, name: 'Test Data' },
        message: 'Success',
      };

      mockRequest.mockResolvedValue(mockResponse);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: { id: 1 },
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      expect(mockSetLoading).toHaveBeenCalledWith(true);
      expect(mockRequest).toHaveBeenCalledWith({ id: 1 });
      expect(mockOnSuccess).toHaveBeenCalledWith(mockResponse.data);
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should handle array response', async () => {
      const mockResponse = [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
      ];

      mockRequest.mockResolvedValue(mockResponse);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: {},
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      expect(mockOnSuccess).toHaveBeenCalledWith(mockResponse);
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should handle special success code', async () => {
      const mockResponse = {
        code: 2304, // Using actual SpecialSuccessCode from constants
        message: 'Special success message',
      };

      mockRequest.mockResolvedValue(mockResponse);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: {},
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      expect(mockOnSuccess).toHaveBeenCalledWith(mockResponse.message);
      expect(mockOnError).not.toHaveBeenCalled();
    });

    it('should handle error response with code 1105', async () => {
      const mockResponse = {
        code: 1105,
        message: 'Validation error',
      };

      mockRequest.mockResolvedValue(mockResponse);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: {},
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      expect(mockOnError).toHaveBeenCalledWith(mockResponse.message);
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('should handle network error', async () => {
      const networkError = new Error('Network Error');
      mockRequest.mockRejectedValue(networkError);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: {},
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      expect(mockSetLoading).toHaveBeenCalledWith(true);
      expect(mockRequest).toHaveBeenCalledWith({});
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('should work without optional callbacks', async () => {
      const mockResponse = {
        code: 0,
        data: { test: 'data' },
      };

      mockRequest.mockResolvedValue(mockResponse);

      // Should not throw error when callbacks are not provided
      await fetchData({
        request: mockRequest,
        params: {},
      });

      expect(mockRequest).toHaveBeenCalledWith({});
    });

    it('should handle timeout scenarios', async () => {
      const timeoutError = new Error('Request timeout');
      mockRequest.mockRejectedValue(timeoutError);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: {},
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      expect(mockSetLoading).toHaveBeenCalledWith(true);
      expect(mockRequest).toHaveBeenCalledWith({});
    });

    it('should handle malformed response', async () => {
      const malformedResponse = null;
      mockRequest.mockResolvedValue(malformedResponse);

      await fetchData({
        setLoading: mockSetLoading,
        request: mockRequest,
        params: {},
        onSuccess: mockOnSuccess,
        onError: mockOnError,
        onFinally: mockOnFinally,
      });

      // Should not call onSuccess for malformed response
      expect(mockOnSuccess).not.toHaveBeenCalled();
      expect(mockSetLoading).toHaveBeenCalledWith(true);
      expect(mockRequest).toHaveBeenCalledWith({});
    });
  });

  describe('API Integration Patterns', () => {
    it('should handle pagination requests', async () => {
      const mockResponse = {
        code: 0,
        data: {
          list: [{ id: 1 }, { id: 2 }],
          total: 100,
          page: 1,
          pageSize: 10,
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      await fetchData({
        request: mockRequest,
        params: { page: 1, pageSize: 10 },
        onSuccess: mockOnSuccess,
      });

      expect(mockRequest).toHaveBeenCalledWith({ page: 1, pageSize: 10 });
      expect(mockOnSuccess).toHaveBeenCalledWith(mockResponse.data);
    });

    it('should handle search requests with filters', async () => {
      const mockResponse = {
        code: 0,
        data: {
          results: [{ id: 1, name: 'Filtered Item' }],
          filters: { category: 'test', status: 'active' },
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const searchParams = {
        keyword: 'test',
        category: 'app',
        status: 'active',
      };

      await fetchData({
        request: mockRequest,
        params: searchParams,
        onSuccess: mockOnSuccess,
      });

      expect(mockRequest).toHaveBeenCalledWith(searchParams);
      expect(mockOnSuccess).toHaveBeenCalledWith(mockResponse.data);
    });

    it('should handle file upload requests', async () => {
      const mockResponse = {
        code: 0,
        data: {
          fileId: 'file123',
          url: 'https://example.com/file123.jpg',
          size: 1024,
        },
      };

      mockRequest.mockResolvedValue(mockResponse);

      const formData = new FormData();
      formData.append('file', new Blob(['test'], { type: 'text/plain' }));

      await fetchData({
        request: mockRequest,
        params: formData,
        onSuccess: mockOnSuccess,
      });

      expect(mockRequest).toHaveBeenCalledWith(formData);
      expect(mockOnSuccess).toHaveBeenCalledWith(mockResponse.data);
    });
  });
});
