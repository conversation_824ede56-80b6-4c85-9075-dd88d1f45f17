import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button, Form, Input, Select } from 'antd';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Example component for testing
const SearchForm: React.FC<{
  onSearch: (values: any) => void;
  loading?: boolean;
}> = ({ onSearch, loading = false }) => {
  const [form] = Form.useForm();

  const handleSubmit = (values: any) => {
    onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
  };

  return (
    <Form
      form={form}
      onFinish={handleSubmit}
      layout="inline"
      data-testid="search-form"
    >
      <Form.Item
        name="keyword"
        label="关键词"
        rules={[{ required: true, message: '请输入关键词' }]}
      >
        <Input placeholder="请输入搜索关键词" />
      </Form.Item>

      <Form.Item name="category" label="分类">
        <Select placeholder="请选择分类" style={{ width: 120 }}>
          <Select.Option value="all">全部</Select.Option>
          <Select.Option value="app">应用</Select.Option>
          <Select.Option value="game">游戏</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>重置</Button>
      </Form.Item>
    </Form>
  );
};

describe('SearchForm Component', () => {
  const mockOnSearch = vi.fn();
  const user = userEvent.setup();

  beforeEach(() => {
    mockOnSearch.mockClear();
  });

  it('should render all form elements', () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    expect(screen.getByTestId('search-form')).toBeInTheDocument();
    expect(screen.getByLabelText('关键词')).toBeInTheDocument();
    expect(screen.getByLabelText('分类')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /搜.*索/ })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /重.*置/ })).toBeInTheDocument();
  });

  it('should show validation error when submitting empty form', async () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    const submitButton = screen.getByRole('button', { name: /搜.*索/ });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('请输入关键词')).toBeInTheDocument();
    });

    expect(mockOnSearch).not.toHaveBeenCalled();
  });

  it('should submit form with valid data', async () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordInput = screen.getByLabelText('关键词');
    const categorySelect = screen.getByLabelText('分类');
    const submitButton = screen.getByRole('button', { name: /搜.*索/ });

    await user.type(keywordInput, 'test keyword');
    await user.click(categorySelect);
    await user.click(screen.getByText('应用'));
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        keyword: 'test keyword',
        category: 'app',
      });
    });
  });

  it('should submit form with only required field', async () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordInput = screen.getByLabelText('关键词');
    const submitButton = screen.getByRole('button', { name: /搜.*索/ });

    await user.type(keywordInput, 'test');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        keyword: 'test',
      });
    });
  });

  it('should reset form fields', async () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordInput = screen.getByLabelText('关键词') as HTMLInputElement;
    const categorySelect = screen.getByLabelText('分类');
    const resetButton = screen.getByRole('button', { name: /重.*置/ });

    // Fill form
    await user.type(keywordInput, 'test keyword');
    await user.click(categorySelect);
    await user.click(screen.getByText('游戏'));

    // Verify form is filled
    expect(keywordInput.value).toBe('test keyword');

    // Reset form
    await user.click(resetButton);

    // Verify form is reset - need to get the input again after reset
    await waitFor(() => {
      const updatedInput = screen.getByLabelText('关键词') as HTMLInputElement;
      expect(updatedInput.value).toBe('');
    });
  });

  it('should show loading state on submit button', () => {
    render(<SearchForm onSearch={mockOnSearch} loading={true} />);

    const submitButton = screen.getByRole('button', { name: /搜.*索/ });
    expect(submitButton).toHaveClass('ant-btn-loading');
  });

  it('should handle keyboard navigation', async () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordInput = screen.getByLabelText('关键词');

    // Focus on input
    await user.click(keywordInput);
    expect(keywordInput).toHaveFocus();

    // Type and submit with Enter
    await user.type(keywordInput, 'test{enter}');

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        keyword: 'test',
      });
    });
  });

  it('should handle select dropdown interactions', async () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    const categorySelect = screen.getByLabelText('分类');

    // Open dropdown
    await user.click(categorySelect);

    // Verify options are visible
    expect(screen.getByText('全部')).toBeInTheDocument();
    expect(screen.getByText('应用')).toBeInTheDocument();
    expect(screen.getByText('游戏')).toBeInTheDocument();

    // Select an option
    await user.click(screen.getByText('游戏'));

    // Verify selection - check that the option was selected by looking for the specific selected item
    await waitFor(() => {
      const selectedItems = screen.getAllByTitle('游戏');
      expect(selectedItems.length).toBeGreaterThan(0);
      expect(selectedItems[0]).toBeInTheDocument();
    });
  });

  it('should maintain form state during re-renders', async () => {
    const { rerender } = render(<SearchForm onSearch={mockOnSearch} />);

    const keywordInput = screen.getByLabelText('关键词') as HTMLInputElement;

    // Fill input
    await user.type(keywordInput, 'persistent value');
    expect(keywordInput.value).toBe('persistent value');

    // Re-render component
    rerender(<SearchForm onSearch={mockOnSearch} loading={true} />);

    // Verify value persists
    expect(keywordInput.value).toBe('persistent value');
  });
});
