/// <reference types="vitest" />
/// <reference types="@testing-library/jest-dom" />

import 'vitest';
import type { TestingLibraryMatchers } from '@testing-library/jest-dom';

declare global {
  namespace Vi {
    interface JestAssertion<T = any>
      extends jest.Matchers<void, T>,
      TestingLibraryMatchers<T, void> {}
  }
}

// 扩展 Window 接口以支持测试中的 Mock
declare global {
  interface Window {
    matchMedia: any;
    ResizeObserver: any;
    IntersectionObserver: any;
  }
}

// 为测试环境添加常用类型
export interface MockFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): ReturnType<T>;
  mockReturnValue(value: ReturnType<T>): MockFunction<T>;
  mockResolvedValue(value: Awaited<ReturnType<T>>): MockFunction<T>;
  mockRejectedValue(value: any): MockFunction<T>;
  mockImplementation(fn: T): MockFunction<T>;
  mockClear(): void;
  mockReset(): void;
  mockRestore(): void;
  toHaveBeenCalled(): void;
  toHaveBeenCalledTimes(times: number): void;
  toHaveBeenCalledWith(...args: Parameters<T>): void;
}

// 测试工具类型
export interface TestUtils {
  render: typeof import('@testing-library/react').render;
  screen: typeof import('@testing-library/react').screen;
  fireEvent: typeof import('@testing-library/react').fireEvent;
  waitFor: typeof import('@testing-library/react').waitFor;
  userEvent: typeof import('@testing-library/user-event').default;
}

// 组件测试 Props 类型
export interface ComponentTestProps {
  [key: string]: any;
}

// API 测试相关类型
export interface ApiTestResponse<T = any> {
  code: number;
  data?: T;
  message?: string;
}

export interface ApiTestRequest {
  url?: string;
  method?: string;
  params?: any;
  data?: any;
}

// 表单测试相关类型
export interface FormTestValues {
  [key: string]: any;
}

export interface FormTestCallbacks {
  onSubmit?: (values: FormTestValues) => void;
  onReset?: () => void;
  onValuesChange?: (changedValues: any, allValues: FormTestValues) => void;
}
