/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:53:19
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 15:00:34
 * @Description: 
 */
/* eslint-disable */
// 该文件由 OneAPI 自动生成，请勿手动修改！

declare namespace TenantAPI {
  type UserListItem = {
    user_id: number;
    account_name: string;
    display_name: string;
    status: number;
    create_time: string;
    update_time: string;
    tnt_name: string;
    email: string;
    role: number;
    tnt_id: number;
    type: number;
    role_id: number;
  };
  type TenantListItem = {
    tnt_id: number;
    tnt_name: string;
    token: string;
    host_prefix: string;
    cs_domain: string;
    email: string;
    status: number;
    pl_status: number;
    hm_status: number;
    tnt_type: number;
  };
}
