/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-15 16:14:29
 * @Description:
 */

declare namespace SupplyAPI {
  type SupplyListItem = {
    tnt_id: number;
    tnt_name: string;
    seller_id: number;
    seller_name: string;
    integration_type: number;
    integration_type_desc: string;
    status: number;
    create_time: string;
    update_time: string;
    channel_type: string;
    device_type: string;
    relationship: string;
    token: string;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    profit_id: number;
    profit_status: number;
    cus_status: number;
    user_id: number;
    pass_burl: number;
    pass_nurl: number;
    pass_lurl: number;
    tagid_status: number;
    rev_track_type: number;
    banner_multi_size: number;
    banner_size_filter: number;
    crid_filter: number;
    status_desc: string;
    host_prefix: string;
    reporting_url?: string;
    doc?: string;
    seller_account_name: string;
    seller_account_status: number;
    pv_domain: string;
    // 以下字段为InfoTable使用
    auth_buyer_id: number;
    auth_buyer_name: string;
    partner_name: string;
    partner_id: number;
    sp_id: number;
    developer_traffic: number;
    win_rate_profit_ratio: number;
  };

  type SellerDemandAuth = {
    buyer_id: number;
    level: number;
    pub_id: number;
    buyer_name: string;
    status: number;
    integration_type: number;
  };

  type SellerPlacement = {
    seller_id: number;
    plm_id: number;
    plm_name: string;
    app_id: number;
    ad_format: number;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    status: number;
    create_time: string;
    update_time: string;
    demand_list: SellerDemandAuth[];
    loading: boolean;
  };

  type SellerAppItem = {
    app_id: number;
    app_name: string;
    seller_id: number;
    bundle: string;
    platform: number;
    store_url: string;
    category: string;
    status: number;
    create_time: string;
    update_time: string;
    adslots: SellerPlacement[];
    demand_list: [];
  };

  type SupplyEndpoint = {
    host_prefix: string;
    token: string;
  };

  type SupplyUser = {
    account_name: string;
    user_id: number;
    status: number;
    doc: string;
    token: string;
  };
}
