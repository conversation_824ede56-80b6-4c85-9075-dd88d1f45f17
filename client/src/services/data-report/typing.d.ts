/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-20 14:39:05
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 16:05:50
 * @Description:
 */
declare namespace FullReportingAPI {
  type KwaiReportItem = {
    tnt_id: number;
    day: string;
    buyer_id: string;
    bundle: string;
    app_id: string;

    buyer: string;
    tenant: string;

    request: number;
    response: number;
    revenue: number;
    impressions: number;
    click: number;
  };

  type PixalateReportItem = {
    day: string;
    month: string;
    tnt: number;
    tnt_id: string;
    seller_id: string;
    buyer_id: string;
    app_bundle_id: string;
    fraud_type: string;
    gross_tracked_ads: string;
    sivt_imp: string;
    givt_imp: string;
    sivt_imp_rate: string;
    givt_imp_rate: string;
    measured_imp: string;
    views: string;
    events: string;
    date: string;
    country: string;
    publisher_id: string;
  };

  type HumanReportItem = {
    seller_id: string;
    buyer_id: string;
    tnt_id: string;
    bundle: string;
    seat: string;
    domain: string;
    publisher_id: string;
    country: string;
    total_events: number;
    valid_traffic: number;
    sivt: number;
    givt: number;
    sivt_automated_browsing: number;
    sivt_false_representation: number;
    sivt_manipulated_behavior: number;
    sivt_misleading_user_interface: number;
    sivt_undisclosed_classification: number;
    givt_data_center: number;
    givt_irregular_pattern: number;
    givt_known_crawler: number;
    givt_false_representation: number;
    givt_misleading_user_interface: number;
    tnt: number;
    day: string;
    month: string;
  };
  type FullReportListItem = {
    id: number;
    tnt_id?: string;
    tnt: string;
    buyer_gross_ecpm: number;
    buyer_gross_revenue: number;
    buyer_net_ecpm: number;
    buyer_net_revenue: number;
    buyer_id: string;
    seller_id: string;
    buyer: string;
    seller: string;
    date: string & { value: string };
    fill_rate: string;
    render_rate: string;
    impression: string;
    impression_rate: string;
    request: string;
    response: string;
    seller_gross_ecpm: number;
    seller_gross_revenue: number;
    seller_net_ecpm: number;
    seller_net_revenue: number;
    total_request: string;
    win: string;
    win_rate: string;
    ad_width: string;
    ad_height: string;
    res_width: string;
    res_height: string;
    profit: number | string;
    profit_rate: number;
    country: string;
    ad_format: string;
    ad_size: string;
    platform: string;
    click: string;
    ctr: string;
    ecpr: string;
    day: string;
    day_hour: string;
    adv_ecpr: string;
    app_name: string;
    app_bundle_id: string;
    adv_config_qps: number;
    pub_config_qps: number;
    region: string;
    http_code: number;
    inventory: number;
    internal_request: number;
    make: string;
    source_tenant: string;
    source_deep: number;
    seller_schain_hop: number;
    seller_schain_complete: any;
    buyer_schain_hop: number;
    buyer_schain_complete: any;
    tmax: number;
    pixalate_be_filtered: number;
    pixalate_passed_filtered: number;
    same_ip: number;
    seller_same_ip: number;
    colKey?: string;
  };
  type BillingListItem = {
    id: number;
    tnt_id: number | string;
    buyer_gross_ecpm: number;
    buyer_gross_revenue: number;
    buyer_net_ecpm: number;
    buyer_net_revenue: number;
    buyer_id: string;
    seller_id: string;
    buyer: string;
    seller: string;
    date: string;
    fill_rate: string;
    render_rate: string;
    impression: string;
    impression_rate: string;
    request: string;
    response: string;
    seller_gross_ecpm: number;
    seller_gross_revenue: number;
    seller_net_ecpm: number;
    seller_net_revenue: number;
    total_request: string;
    win: string;
    win_rate: string;
    ad_width: string;
    ad_height: string;
    profit: number | string;
    profit_rate: number;
    country: string;
    ad_format: string;
    ad_size: string;
    platform: string;
    click: string;
    ctr: string;
    ecpr: string;
    day: string;
    day_hour: string;
    adv_ecpr: string;
    partner_id: string;
    partner_name: string;
  };

  type ExportedReportItem = {
    id: number;
    name: string;
    type: string;
    status: number;
    create_time: string;
    query_condition: string;
    url: string;
    type_desc: string;
    status_desc: string;
    err_msg: string;
  };

  type MonthlyReportItem = {
    id: number;
    tnt_id: string;
    date: string;
    buyer_net_revenue: number;
    seller_net_revenue: number;
    profit: number;
    profit_rate: number;
  };
}
