/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 18:25:17
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-27 10:59:36
 * @Description:
 */
import { ButtonType, ColumnsType } from '@/components/Table/BackTable';
import { TopBarSearchItem } from '@/components/TopBar';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { PlusOutlined } from '@ant-design/icons';
import HoverTooltip from '@/components/Tooltip/HoverTooltip';

// 角色类型
export const RoleType = {
  'Super Administer': 1, // 超管角色 新增租户自动生成
  'Normal Role': 2, // 普通角色
};

export const RoleTypeMap: { [key: number]: string } = {
  1: '超级管理员',
  2: '普通角色',
};

export const RoleColumns: ColumnsType<AdminSettingAPI.RoleItem>[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '角色名称',
    dataIndex: 'role_name',
    width: 150,
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 130,
    render: (_) => RoleTypeMap[_] || '-',
  },
  {
    title: '备注',
    width: 120,
    dataIndex: 'remark',
    ellipsis: { showTitle: false },
    render: (_) => {
      return (
        <HoverTooltip title={_}>
          <span>{_ || '-'}</span>
        </HoverTooltip>
      );
    },
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    width: 160,
  }
];

export const RoleSearchOption: TopBarSearchItem[] = [
  {
    type: 'select',
    key: 'id',
    mode: 'multiple',
    value: '',
    options: [],
    placeholder: '请选择角色',
    name: '角色',
  },
];

export const RoleBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Admin Setting',
    icon: 'rix-advanced',
  },
  {
    name: 'Role',
  },
];

export const RoleBtnOptions: ButtonType[] = [
  {
    label: '新增角色',
    type: 'primary',
    size: 'small',
    icon: <PlusOutlined />,
    key: 'create',
    disabled: false,
  },
];

export const RoleTabItems = [
  {
    label: '角色列表',
    key: 1,
  },
  {
    label: '权限设置',
    key: 2,
  },
];

export const RoleTab = {
  'Role List': 1,
  Permission: 2,
};
