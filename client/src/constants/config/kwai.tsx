/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 14:13:36
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-29 10:48:51
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusDesc, StatusOptions } from '@/constants';
import moment from 'moment';
import { ColumnType } from '@/components/Table/FrontTable';

export const KwaBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Config',
    icon: 'rix-advanced',
  },
  {
    name: 'Kwai Placement',
  },
];

export const KwaColumnOptions: ColumnType<ConfigAPI.KwaiPlacementItem>[] = [
  {
    title: 'Tenant',
    width: 180,
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={`${params.tnt_name}(${params.tnt_id})`}>
        <span>
          {params.tnt_name}({_})
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Demand',
    width: 180,
    dataIndex: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={`${params.buyer_name}(${params.buyer_id})`}>
        <span>
          {params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'}
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Bundle',
    width: 120,
    dataIndex: 'bundle',
  },
  {
    title: 'App ID',
    width: 150,
    dataIndex: 'app_id',
    render: (_) => _ || '-',
  },
  {
    title: 'Native Unit ID',
    width: 160,
    dataIndex: 'native_pid',
    render: (_) => _ || '-',
  },
  {
    title: 'Interstitial Video Unit ID',
    width: 200,
    dataIndex: 'inters_pid',
    render: (_) => _ || '-',
  },
  {
    title: 'Reward Video Unit ID',
    width: 220,
    dataIndex: 'reward_pid',
    render: (_) => _ || '-',
  },
  {
    title: 'Token',
    width: 160,
    dataIndex: 'token',
  },
  {
    title: 'Mixed Status',
    width: 130,
    dataIndex: 'mixed_status',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Modify By',
    width: 120,
    dataIndex: 'op_name',
  },
  {
    title: 'Updated On',
    width: 160,
    dataIndex: 'update_time',
    render: (_: string) => {
      return moment(_).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: 'Status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
];

export const KwaSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Demand',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    value: '',
    mode: 'multiple', // 多行的过滤判断
    isNoExact: true, // 模糊匹配
  },
  {
    name: 'App ID',
    type: 'bundle',
    key: 'app_id',
    value: '',
    mode: 'multiple', // 多行的过滤判断
    isNoExact: true, // 模糊匹配
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
];

export const DefaultFormData = {
  buyer_id: undefined,
  tnt_id: undefined,
  bundle: undefined,
  app_id: undefined,
  native_pid: undefined,
  inters_pid: undefined,
  reward_pid: undefined,
  mixed_status: 2,
  status: 1,
};
