/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-04-03 16:40:18
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 19:04:57
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusOptions } from '@/constants';
import moment from 'moment';
import { ColumnType } from '@/components/Table/FrontTable';

export const PixlBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Config',
    icon: 'rix-advanced',
  },
  {
    name: 'Pixalate Prebid',
  },
];

export const PixlColumnOptions: ColumnType<ConfigAPI.PixlItem>[] = [
  {
    title: 'Tenant',
    width: 180,
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      const tnt_name = params.tnt_id === 0 ? 'All Tenants' : params.tnt_name;
      return (
        <HoverToolTip title={`${tnt_name}(${params.tnt_id})`}>
          <span>
            {tnt_name}({_})
          </span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Advertiser',
    width: 180,
    dataIndex: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={`${params.buyer_name}(${_})`}>
          <span>
            {params.buyer_name}({_})
          </span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Publisher',
    width: 180,
    dataIndex: 'seller_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={`${params.seller_name}(${_})`}>
          <span>
            {params.seller_name}({_})
          </span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Modify By',
    width: 120,
    dataIndex: 'op_name',
  },
  {
    title: 'Updated On',
    width: 160,
    dataIndex: 'update_time',
    render: (_: string) => {
      return moment(_).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: 'Status',
    key: 'status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
];

export const PixlSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    mode: 'multiple',
    options: [],
  },

  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
];

export const DefaultFormData = {
  tnt_id: undefined,
  bundle: undefined,
  type: 1,
};
