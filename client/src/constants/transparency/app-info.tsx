/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-19 17:52:21
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:19:12
 * @Description:
 */

import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/es/table';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import EllipsisPopover from '@/components/EllipsisPopover';

export const AppInfoSearchOption: TopBarSearchItem[] = [
  {
    name: 'APP Bundle ID',
    type: 'bundle',
    key: 'app_bundle_id',
    value: '',
    tooltip: 'Separated by comma or space',
    required: true,
    limit: 500,
    placeholder: 'Input to filter/add,separated by comma/space',
  },
  {
    name: 'AAT Domain',
    type: 'bundle',
    key: 'aat_domain',
    value: '',
    tooltip: 'Separated by comma or space',
    limit: 500,
    required: true,
    placeholder: 'Input to filter/add,separated by comma/space',
  },
  {
    name: 'Supply Tag',
    type: 'select',
    key: 'supply_tag',
    value: '',
    options: [],
  },
];

export const StatusDescMap: { [key: number]: string } = {
  1: 'Success',
  2: 'Not Found',
  3: 'Failed',
};

export const AppInfoColumns: ColumnProps<TransparencyAPI.AppInfoItem>[] = [
  {
    title: 'App Bundle ID',
    width: 120,
    dataIndex: 'app_bundle_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.app_bundle_id}>
          {params.store_url ? (
            <a href={params.store_url} target="_blank" rel="noreferrer">
              {params.app_bundle_id}
            </a>
          ) : (
            <span>{params.app_bundle_id}</span>
          )}
        </HoverToolTip>
      );
    },
  },
  {
    title: 'App Name',
    width: 220,
    dataIndex: 'app_name',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.app_name}>
          <span>{params.app_name}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Developer Name',
    width: 160,
    dataIndex: 'developer_name',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.developer_name}>
          <span>{params.developer_name}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Developer Website',
    width: 160,
    dataIndex: 'developer_website',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.developer_website}>
          <span>{params.developer_website}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Category',
    width: 160,
    dataIndex: 'category',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.category}>
          <span>{params.category}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Rating Score',
    width: 120,
    dataIndex: 'rating_score',
    ellipsis: { showTitle: false },
    render: (rating_score) => <span>{rating_score ?? '-'}</span>,
  },
  {
    title: 'Downloads',
    width: 180,
    dataIndex: 'downloads_info',
    ellipsis: { showTitle: false },
    render: (downloads_info) => <span>{downloads_info || '-'}</span>,
  },
  {
    title: 'AAT Domain',
    width: 160,
    dataIndex: 'aat_domain',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.aat_domain}>
          <span>{params.aat_domain}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Manual AAT Domain',
    width: 160,
    dataIndex: 'multi_aat_domain',
    ellipsis: { showTitle: false },
    render: (multi_aat_domain) => {
      if (!multi_aat_domain) return '-';
      return (
        <HoverToolTip title={multi_aat_domain}>
          <span>{multi_aat_domain}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Support AAT',
    width: 120,
    dataIndex: 'support_aat',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <>
        {+_ === 1 ? (
          <a href={params.aat_url} target="_blank" rel="noreferrer">
            Yes
          </a>
        ) : (
          <span>No</span>
        )}
      </>
    ),
  },
  {
    title: (
      <>
        <span>Refresh</span>
        <Tooltip title="Enable or pause refresh">
          <QuestionCircleOutlined
            style={{ paddingLeft: '5px', cursor: 'pointer' }}
          />
        </Tooltip>
      </>
    ),
    key: 'Update Tag',
    width: 120,
    fixed: 'right',
    dataIndex: 'update_tag',
  },
];

const renderWithEllipsisPopover = (data: string[] | undefined) => {
  if (!data?.length) return '-';
  if (data.length === 1)
    return (
      <HoverToolTip title={data[0]}>
        <span>{data[0]}</span>
      </HoverToolTip>
    );
  return <EllipsisPopover dataSource={data} />;
};

export const SellerColumns: ColumnProps<TransparencyAPI.AppInfoItem>[] = [
  {
    title: 'Seller ID',
    width: 120,
    dataIndex: 'seller_id',
    ellipsis: { showTitle: false },
    render: renderWithEllipsisPopover,
  },
  {
    title: 'Seller Type',
    width: 120,
    dataIndex: 'seller_type',
    ellipsis: { showTitle: false },
    render: renderWithEllipsisPopover,
  },
  {
    title: 'Schain Name',
    width: 120,
    dataIndex: 'name',
    ellipsis: { showTitle: false },
    render: renderWithEllipsisPopover,
  },
];

export const AppInfoBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Transparency',
    icon: 'rix-transparency',
  },
  {
    name: 'App Info',
  },
];

// bundle_id, domain, name, seller_id, seller_type
export const SellersJsonFields = [
  {
    label: 'App Bundle ID',
    value: 'app_bundle_id',
  },
  {
    label: 'Domain',
    value: 'aat_domain',
  },
  {
    label: 'Manual Domain',
    value: 'multi_aat_domain',
  },
  {
    label: 'Name',
    value: 'name',
  },
  {
    label: 'Seller ID',
    value: 'seller_id',
  },
  {
    label: 'Seller Type',
    value: 'seller_type',
  },
  {
    label: 'Supply Tag',
    value: 'supply_tag',
  },
];
