/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 16:13:41
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-28 10:45:57
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';
import HoverTooltip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import EllipsisPopover from '@/components/EllipsisPopover';

// 合作方类型
export const PartnerType = {
  Publisher: 1,
  Advertiser: 2,
  'Advertiser & Publisher': 3,
};

const PartnerTypeMap: { [key: number]: string } = {
  1: 'Pub',
  2: 'Adv',
  3: 'Adv + Pub',
};

export const TypeOptions = [
  { label: 'Adv', value: 2 },
  { label: 'Pub', value: 1 },
  { label: 'Adv + Pub', value: 3 },
];

/**
 * @deprecated 没有被使用，后续需要删除
 */
export const PartnerColumns: ColumnProps<PartnerAPI.PartnerListItem>[] = [
  {
    title: 'Name',
    dataIndex: 'partner_name',
    width: 180,
    ellipsis: { showTitle: false },
    render: (text) => (
      <HoverTooltip title={text}>
        <span>{text}</span>
      </HoverTooltip>
    ),
  },
  {
    title: 'ID',
    dataIndex: 'partner_id',
    width: 70,
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined
            style={{ paddingLeft: '5px', cursor: 'pointer' }}
          />
        </Tooltip>
      </div>
    ),
    dataIndex: 'type',
    width: 80,
    render: (_) => PartnerTypeMap[_],
  },
  {
    title: 'Advertisers',
    dataIndex: 'buyers',
    width: 280,
    ellipsis: { showTitle: false },
    render: (_) =>
      Array.isArray(_) && _.length > 0 ? (
        <EllipsisPopover
          dataSource={_.map((v: any) => `${v.buyer_name}(${v.buyer_id})`)}
        />
      ) : (
        '-'
      ),
  },
  {
    title: 'Publishers',
    dataIndex: 'sellers',
    width: 280,
    ellipsis: { showTitle: false },
    render: (_) =>
      Array.isArray(_) && _.length > 0 ? (
        <EllipsisPopover
          dataSource={_.map((v: any) => `${v.seller_name}(${v.seller_id})`)}
        />
      ) : (
        '-'
      ),
  },
  {
    title: 'Operation',
    dataIndex: 'operate',
    fixed: 'right',
    width: 90,
  },
];

export const PartnerSearchOption: TopBarSearchItem[] = [
  {
    name: 'Partner',
    type: 'select',
    key: 'partner_id',
    value: '',
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Partner',
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: '',
    tooltip: (
      <span>
        Adv = Advertiser
        <br />
        Pub = Publisher
      </span>
    ),
    options: TypeOptions,
    mode: 'multiple',
    placeholder: 'Please Select Type',
  },
];

export const PartnerBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Partner',
    icon: 'rix-partner',
  },
];
