/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-02-27 18:03:45
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-09 15:39:36
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import EllipsisPopover from '@/components/EllipsisPopover';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { AdFormatOptions } from '@/constants';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { formatMoney } from '@/utils';
import { FormatExportValueMapType } from '@/utils/export-file';
import { APIFrameworksMapByValue, DeviceBrandOptions } from '@rixfe/rix-tools';
import { ColumnProps } from 'antd/lib/table';

export const SchainMap: Record<number, string> = {
  0: 'Incomplete',
  1: 'Complete',
};

const RegionOptions = [
  { label: 'USE', value: 'USE' },
  { label: 'APAC', value: 'APAC' },
  { label: 'EUW', value: 'EUW' },
];
const YesNoOptions = [
  {
    label: 'Yes',
    value: 1,
  },
  {
    label: 'No',
    value: 0,
  },
];
const InventoryOptions = [
  { label: 'Inapp', value: 1 },
  { label: 'Website', value: 2 },
  { label: 'UnKnown', value: 0 },
];
export const InventoryMapDesc: API.StringType = {
  1: 'Inapp',
  2: 'Website',
  0: 'UnKnown',
};

const HttpCodeDesc: API.StringType = {
  0: 'Normal',
  408: 'Timeout',
  429: 'Too Many Requests',
  10: 'Connect Failed',
};
const HttpCodeOptions = [
  { label: 'Normal', value: 0 },
  { label: 'Timeout', value: 408 },
  { label: 'Too Many Requests', value: 429 },
  { label: 'Connect Failed', value: 10 },
];

const RequestTypeOptions = [
  { label: 'Internal', value: 1 },
  { label: 'External', value: 0 },
];

const RequestTypeMapDesc: API.StringType = {
  1: 'Internal',
  0: 'External',
};

// TODO 映射重复，字段待优化
const DeviceTypeOptions = [
  { label: 'Mobile/Tablet', value: 1 },
  { label: 'PC', value: 2 },
  { label: 'CTV', value: 3 },
  { label: 'Phone', value: 4 },
  { label: 'Tablet', value: 5 },
  { label: 'Connected Device', value: 6 },
  { label: 'Set Top Box', value: 7 },
  { label: 'Unknown', value: 0 },
];

const DeviceTypeMapDesc: { [key: number]: string } = {
  1: 'Mobile/Tablet',
  2: 'PC',
  3: 'CTV',
  4: 'Phone',
  5: 'Tablet',
  6: 'Connected Device',
  7: 'Set Top Box',
  0: 'Unknown',
};

export const AllMetricsOptions = [
  { label: 'Advertiser Net Revenue', value: 'buyer_net_revenue' },
  { label: 'Publisher Net Revenue', value: 'seller_net_revenue' },
  {
    label: 'Profit',
    value: 'profit',
    tooltip: {
      showTitle: true,
      title: 'sum(Advertiser Net Revenue) - sum(Publisher Net Revenue)',
    },
  },
  { label: 'Request', value: 'request' },
  { label: 'Total Request', value: 'total_request' },
  { label: 'Block Request', value: 'block_request' },
  { label: 'Out Request', value: 'out_request' },
  { label: 'Response', value: 'response' },
  { label: 'Win', value: 'win' },
  { label: 'Impression(ADM)', value: 'impression' },
  {
    label: 'Impression(Pay)',
    value: 'seller_payment_impression',
    tooltip: { showTitle: true, title: 'For Publisher Payment' },
  },
  { label: 'Click', value: 'click' },
  { label: 'Bid Floor', value: 'total_seller_bid_floor' },
  {
    label: 'Fill Rate',
    value: 'fill_rate',
    tooltip: { showTitle: true, title: '(sum(Response) * 100) / sum(Request)' },
  },
  { label: 'Advertiser Gross Revenue', value: 'buyer_gross_revenue' },
  { label: 'Publisher Gross Revenue', value: 'seller_gross_revenue' },
  {
    label: 'eCPR',
    value: 'ecpr',
    tooltip: {
      showTitle: true,
      title: 'sum(Advertiser Net Revenue) * 1000000 / sum(Publisher Request)',
    },
  },
  {
    label: 'Advertiser eCPR',
    value: 'adv_ecpr',
    tooltip: {
      showTitle: true,
      title: 'sum(Advertiser Net Revenue) * 1000000 / sum(Advertiser Request)',
    },
  },
  {
    label: 'Click Rate',
    value: 'click_rate',
    tooltip: { showTitle: true, title: 'sum(Click) / sum(Impression(ADM))' },
  },
  {
    label: 'Profit Rate',
    value: 'profit_rate',
    tooltip: {
      showTitle: true,
      title:
        '(sum(Advertiser Net Revenue) - sum(Publisher Net Revenue))) / sum(Advertiser Net Revenue)',
    },
  },
  {
    label: 'Win Rate',
    value: 'win_rate',
    tooltip: { showTitle: true, title: '(sum(win) *100 / sum(response))' },
  },
  {
    label: 'Render Rate',
    value: 'impression_rate',
    tooltip: {
      showTitle: true,
      title: '(sum(Impression(ADM)) * 100) / sum(Response)',
    },
  },
  {
    label: 'Advertiser Gross eCpm',
    value: 'buyer_gross_ecpm',
    tooltip: {
      showTitle: true,
      title: '(sum(Advertiser Net eCpm) * 1000) / sum(Request)',
    },
  },
  {
    label: 'Advertiser Net eCpm',
    value: 'buyer_net_ecpm',
    tooltip: {
      showTitle: true,
      title: '(sum(Advertiser Net Revenue) * 1000) / sum(Impression(ADM))',
    },
  },
  // 新增 Publisher Net eCpm
  {
    label: 'Publisher Net eCpm',
    value: 'seller_net_ecpm',
    tooltip: {
      showTitle: true,
      title: '(sum(Publisher Net Revenue) * 1000) / sum(Impression(ADM))',
    },
  },
  {
    label: 'Bid Price',
    value: 'bid_price',
    tooltip: {
      showTitle: true,
      title: 'sum(Total Response Price) / sum(Response)',
    },
  },
  {
    label: 'QPS (Real)',
    value: 'real_qps',
    tooltip: { showTitle: true, title: 'Total Request / Secs' },
  },
  {
    label: 'Advertiser QPS (Config)',
    value: 'adv_config_qps',
  },
  {
    label: 'Publisher QPS (Config)',
    value: 'pub_config_qps',
  },
  {
    label: 'CPC',
    value: 'cpc',
    tooltip: { showTitle: true, title: 'sum(Advertiser Net Revenue) / sum(Click)' },
  }
];
const MetricsOptions = [
  { label: 'Advertiser Net Revenue', value: 'buyer_net_revenue' },
  { label: 'Publisher Net Revenue', value: 'seller_net_revenue' },
  {
    label: 'Profit',
    value: 'profit',
    tooltip: {
      showTitle: true,
      title: 'sum(Advertiser Net Revenue) - sum(Publisher Net Revenue)',
    },
  },
  { label: 'Request', value: 'request' },
  { label: 'Total Request', value: 'total_request' },
  { label: 'Block Request', value: 'block_request' },
  { label: 'Out Request', value: 'out_request' },
  { label: 'Response', value: 'response' },
  { label: 'Win', value: 'win' },
  { label: 'Impression(ADM)', value: 'impression' },
  {
    label: 'Impression(Pay)',
    value: 'seller_payment_impression',
    tooltip: { showTitle: true, title: 'For Publisher Payment' },
  },
  { label: 'Click', value: 'click' },
  { label: 'Bid Floor', value: 'total_seller_bid_floor' },
  {
    label: 'Fill Rate',
    value: 'fill_rate',
    tooltip: { showTitle: true, title: '(sum(Response) * 100) / sum(Request)' },
  },
  {
    label: 'eCPR',
    value: 'ecpr',
    tooltip: {
      showTitle: true,
      title: 'sum(Advertiser Net Revenue) * 1000000 / sum(Publisher Request)',
    },
  },
  {
    label: 'Advertiser eCPR',
    value: 'adv_ecpr',
    tooltip: {
      showTitle: true,
      title: 'sum(Advertiser Net Revenue) * 1000000 / sum(Advertiser Request)',
    },
  },
  {
    label: 'Click Rate',
    value: 'click_rate',
    tooltip: { showTitle: true, title: 'sum(Click) / sum(Impression(ADM))' },
  },
  {
    label: 'Profit Rate',
    value: 'profit_rate',
    tooltip: {
      showTitle: true,
      title:
        '(sum(Advertiser Net Revenue) - sum(Publisher Net Revenue))) / sum(Advertiser Net Revenue)',
    },
  },
  {
    label: 'Win Rate',
    value: 'win_rate',
    tooltip: { showTitle: true, title: '(sum(win) *100 / sum(response))' },
  },
  {
    label: 'Render Rate',
    value: 'impression_rate',
    tooltip: {
      showTitle: true,
      title: '(sum(Impression(ADM)) * 100) / sum(Response)',
    },
  },

  {
    label: 'Advertiser Net eCpm',
    value: 'buyer_net_ecpm',
    tooltip: {
      showTitle: true,
      title: '(sum(Advertiser Net Revenue) * 1000) / sum(Impression(ADM))',
    },
  },
  {
    label: 'Publisher Net eCpm',
    value: 'seller_net_ecpm',
    tooltip: {
      showTitle: true,
      title: '(sum(Publisher Net Revenue) * 1000) / sum(Impression(ADM))',
    },
  },
  {
    label: 'Bid Price',
    value: 'bid_price',
    tooltip: {
      showTitle: true,
      title: 'sum(Total Response Price) / sum(Response)',
    },
  },
  {
    label: 'QPS (Real)',
    value: 'real_qps',
    tooltip: { showTitle: true, title: 'Total Request / Secs' },
  },
  {
    label: 'Advertiser QPS (Config)',
    value: 'adv_config_qps',
  },
  {
    label: 'Publisher QPS (Config)',
    value: 'pub_config_qps',
  },
  {
    label: 'Adv Req Time',
    value: 'avg_dsp_cost_time',
    tooltip: {
      showTitle: true,
      title: 'sum(Adv Request Cost Time) / sum(Request)',
    },
  },
  {
    label: 'Adv Resp Time',
    value: 'avg_response_cost_time',
    tooltip: {
      showTitle: true,
      title: 'sum(Adv Response Cost Time) / sum(Response)',
    },
  },
  {
    label: 'Imp Banner JS',
    value: 'imp_banner_js',
  },
  {
    label: 'Imp Banner Pixel',
    value: 'imp_banner_pixel',
  },
  {
    label: 'Imp Banner Mraid',
    value: 'imp_banner_mraid',
  },
  {
    label: 'Tmax',
    value: 'tmax',
    tooltip: {
      showTitle: true,
      title: 'sum(Tmax) / sum(Request)',
    },
  },
  {
    label: 'PX Prebid Scan Rate',
    value: 'pixalate_be_filtered_rate',
    tooltip: {
      showTitle: true,
      title: 'sum(PX Prebid Scans) / sum(Request)',
    },
  },
  {
    label: 'PX Prebid Pass Rate',
    value: 'pixalate_passed_filtered_rate',
    tooltip: {
      showTitle: true,
      title: 'sum(PX Prebid Passed) / sum(PX Prebid Scans)',
    },
  },
  {
    label: 'Same IP Rate(ADM)',
    value: 'same_ip_rate',
    tooltip: {
      showTitle: true,
      title: 'sum(Same IP) / sum(Impression(ADM))',
    },
  },
  {
    label: 'Same IP Rate(Pay)',
    value: 'seller_same_ip_rate',
    tooltip: {
      showTitle: true,
      title: 'sum(Pub Same IP) / sum(Impression(Pay))',
    },
  },
  // admin管理平台fulling reporting 加一下cpc的指标 （cpc = adv net revenue/click）
  {
    label: 'CPC',
    value: 'cpc',
    tooltip: { showTitle: true, title: 'sum(Advertiser Net Revenue) / sum(Click)' },
  }
];
export const DimensionsOptions = [
  { label: 'Date', value: 'day' },
  { label: 'Hour', value: 'day_hour' },
  { label: 'Tenant', value: 'tnt' },
  { label: 'Publisher', value: 'seller_id' },
  { label: 'Advertiser', value: 'buyer_id' },
  { label: 'Bundle', value: 'app_bundle_id' },
  { label: 'Unit ID', value: 'placement_id' },
  { label: 'Ad Format', value: 'ad_format' },
  { label: 'Ad Size', value: 'ad_size' },
  { label: 'Bid Ad Width', value: 'res_width' },
  { label: 'Bid Ad Height', value: 'res_height' },
  { label: 'Country', value: 'country' },
  { label: 'Platform', value: 'platform' },
  { label: 'Server Region', value: 'region' },
  { label: 'Adomain', value: 'ad_domain' },
  { label: 'Inventory', value: 'inventory' },
  { label: 'Http Code', value: 'http_code' },
  { label: 'Request Type', value: 'internal_request' },
  { label: 'Source Tenant', value: 'source_tenant' },
  { label: 'Source Deep', value: 'source_deep' },
  { label: 'Schain Hop', value: 'seller_schain_hop' },
  { label: 'Schain Complete', value: 'seller_schain_complete' },
  { label: 'Demand Schain Hop', value: 'buyer_schain_hop' },
  { label: 'Demand Schain Complete', value: 'buyer_schain_complete' },
  // make 后续代码迁移到运营平台时 使用硬编码，仅支持 1052 租户
  { label: 'Device Brand', value: 'make' },
  { label: 'Device Type', value: 'device_type' },
  // media protocol
  { label: 'Bid Api', value: 'res_api' },
];

export const DateType = {
  Null: 0,
  Day: 1,
  Hour: 2,
  Month: 3,
};

export const DashboardSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    timeLimit: 15,
    dimensionTimeLimit: [
      {
        dimensionKey: 'app_bundle_id',
        limit: 7,
        searchLimit: 15,
        hourLimit: 1,
      },
      {
        dimensionKey: 'placement_id',
        limit: 7,
        searchLimit: 15,
        hourLimit: 1,
      },
      {
        dimensionKey: 'ad_domain',
        limit: 7,
        searchLimit: 15,
        hourLimit: 1,
      },
    ],
  },
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: [],
    options: [],
    mode: 'multiple',
    rules: [{ required: true, message: 'Please Select Tenant' }],
    limitSelectCount: 5,
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'app_bundle_id',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space',
  },
  {
    type: 'input',
    name: 'Unit ID',
    value: '',
    key: 'placement_id',
  },
  {
    name: 'Ad Format',
    key: 'ad_format',
    mode: 'multiple',
    value: '',
    type: 'select',
    options: AdFormatOptions,
  },
  {
    name: 'Ad Size',
    key: 'ad_size',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: [],
  },
  {
    name: 'Platform',
    key: 'platform',
    type: 'select',
    value: '',
    mode: 'multiple',
    options: Object.keys(demandCampaign.MoblieOS).map((item) => {
      return {
        value: item,
        label: (demandCampaign.MoblieOS as any)[item],
      };
    }),
  },
  {
    name: 'Country',
    key: 'country',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: Object.keys(demandCampaign.Country).map((item) => {
      return {
        value: item,
        label: (demandCampaign.Country as any)[item],
      };
    }),
  },
  {
    name: 'Server Region',
    key: 'region',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: RegionOptions.map((item) => {
      return {
        value: item.value,
        label: item.label,
      };
    }),
  },

  {
    name: 'Adomain',
    key: 'ad_domain',
    type: 'bundle',
    mode: 'multiple',
    value: '',
    placeholder: 'Support multiple Adomains',
  },
  {
    name: 'Inventory',
    key: 'inventory',
    type: 'select',
    mode: 'multiple',
    value: '',
    tooltip: 'whether the request is Inapp or Website',
    options: InventoryOptions,
  },
  {
    name: 'Instl',
    key: 'instl',
    type: 'select',
    mode: 'multiple',
    value: '',
    tooltip: 'whether the ad is interstitial or full screen',
    options: YesNoOptions.map((item) => {
      return {
        value: item.value,
        label: item.label,
      };
    }),
  },
  // {
  //   name: 'Time Slot',
  //   type: 'selectAll',
  //   key: 'hour',
  //   value: [],
  //   options: Object.keys(demandCampaign.TimeSlot).map((item) => {
  //     return {
  //       value: item,
  //       label: `${(demandCampaign as any).TimeSlot[item]}`,
  //     };
  //   }),
  //   mode: 'multiple',
  // },
  {
    name: 'Http Code',
    type: 'select',
    key: 'http_code',
    value: [],
    mode: 'multiple',
    options: HttpCodeOptions,
  },
  {
    name: 'Request Type',
    type: 'select',
    key: 'internal_request',
    value: [],
    mode: 'multiple',
    options: RequestTypeOptions,
  },
  {
    name: 'Adv Partner',
    key: 'adv_partner_id',
    type: 'selectAll',
    mode: 'multiple',
    value: [],
    options: [],
    placeholder: 'Please select Adv Partners',
  },
  {
    name: 'Pub Partner',
    key: 'pub_partner_id',
    type: 'selectAll',
    mode: 'multiple',
    value: [],
    options: [],
    placeholder: 'Please select Pub Partners',
  },
  {
    name: 'Crid',
    key: 'res_crid',
    type: 'bundle',
    mode: 'multiple',
    value: [],
    placeholder: 'Support multiple Crids',
  },
  {
    name: 'Cid',
    key: 'res_cid',
    type: 'bundle',
    mode: 'multiple',
    value: [],
    placeholder: 'Support multiple Cids',
  },
  {
    name: 'Device Brand',
    key: 'make',
    type: 'select',
    mode: 'multiple',
    value: [],
    options: DeviceBrandOptions,
  },
  {
    name: 'Device Type',
    key: 'device_type',
    type: 'select',
    mode: 'multiple',
    value: [],
    options: DeviceTypeOptions,
  },
  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: DimensionsOptions,
  },
  {
    name: 'Metrics',
    labelIcon: 'rix-edit',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: MetricsOptions,
  },
];

export const DashboardBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report',
  },
  {
    name: 'Full Reporting',
  },
];

export const DashBoardDefaultMetrics = [
  'buyer_net_revenue',
  'request',
  'total_request',
  'total_seller_bid_floor',
  'fill_rate',
  'ecpr',
  'click_rate',
  'profit_rate',
  'win_rate',
  'bid_price',
  'real_qps',
];

export const DashboardDefaultDimension = ['day'];

export const DashboardAllColumns: ColumnProps<FullReportingAPI.FullReportListItem>[] =
  Object.seal([
    {
      title: 'Date',
      key: 'day',
      fixed: 'left',
      width: 160,
      dataIndex: 'date',
      render: (txt, params) => <>{params.date}</>,
      sorter: true,
    },
    {
      title: 'Hour',
      dataIndex: 'date',
      fixed: 'left',
      width: 160,
      key: 'day_hour',
      render: (txt, params) => <>{params.date}</>,
      sorter: true,
    },
    {
      title: 'Tenant',
      dataIndex: 'tnt',
      width: 150,
      key: 'tnt',
      sorter: true,
      ellipsis: { showTitle: false },
      render: (txt, params) => (
        <HoverToolTip title={params.tnt}>
          <span>{params.tnt}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Publisher',
      dataIndex: 'seller_id',
      width: 180,
      key: 'seller_id',
      ellipsis: { showTitle: false },
      sorter: true,
      render: (_, params) => (
        <HoverToolTip title={params.seller}>
          <span>{params.seller}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Advertiser',
      dataIndex: 'buyer_id',
      width: 180,
      key: 'buyer_id',
      sorter: true,
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={params.buyer}>
          <span>{params.buyer}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Bundle',
      dataIndex: 'app_bundle_id',
      width: 150,
      key: 'app_bundle_id',
    },
    {
      title: 'Unit ID',
      dataIndex: 'placement_id',
      width: 150,
      key: 'placement_id',
      render: (txt, params) => {
        if (!txt) return <span>-</span>;
        return <>{txt}</>;
      },
    },
    {
      title: 'Ad Format',
      dataIndex: 'ad_format',
      width: 150,
      key: 'ad_format',
      sorter: true,
    },
    {
      title: 'Ad Size',
      dataIndex: 'ad_size',
      width: 150,
      key: 'ad_size',
    },
    {
      title: 'Bid Ad Width',
      dataIndex: 'res_width',
      width: 150,
      key: 'res_width',
      sorter: true,
    },
    {
      title: 'Bid Ad Height',
      dataIndex: 'res_height',
      width: 150,
      key: 'res_height',
      sorter: true,
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      width: 120,
      // sorter: true,
      key: 'platform',
    },
    {
      title: 'Country',
      dataIndex: 'country',
      width: 150,
      key: 'country',
    },
    {
      title: 'Server Region',
      key: 'region',
      dataIndex: 'region',
      // sorter: true,
      width: 150,
    },
    {
      title: 'Adomain',
      key: 'ad_domain',
      dataIndex: 'ad_domain',
      width: 220,
      render: (txt: string, params) => {
        let list = (txt && txt.split(',').filter((v) => v)) || [];
        return (
          (Array.isArray(list) && list.length && (
            <EllipsisPopover dataSource={list} />
          )) ||
          '-'
        );
      },
    },
    {
      title: 'Inventory',
      key: 'inventory',
      dataIndex: 'inventory',
      width: 110,
      render: (_) => <>{InventoryMapDesc[_]}</>,
    },
    {
      title: 'Http Code',
      key: 'http_code',
      dataIndex: 'http_code',
      width: 200,
      render: (_) => (
        <span style={{ color: _ !== 0 ? 'red' : 'green' }}>
          {HttpCodeDesc[_] ? `${_} (${HttpCodeDesc[_]})` : _}
        </span>
      ),
    },
    {
      title: 'Request Type',
      key: 'internal_request',
      dataIndex: 'internal_request',
      width: 150,
      render: (txt: string) => <>{RequestTypeMapDesc[txt]}</>,
    },
    {
      title: 'Source Tenant',
      key: 'source_tenant',
      dataIndex: 'source_tenant',
      width: 150,
      ellipsis: { showTitle: false },
      render: (txt: string) => (
        <HoverToolTip title={txt}>
          <span>{txt}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Source Deep',
      key: 'source_deep',
      dataIndex: 'source_deep',
      width: 150,
    },
    {
      title: 'Schain Hop',
      key: 'seller_schain_hop',
      dataIndex: 'seller_schain_hop',
      width: 150,
    },
    {
      title: 'Schain Complete',
      key: 'seller_schain_complete',
      dataIndex: 'seller_schain_complete',
      width: 200,
    },
    {
      title: 'Demand Schain Hop',
      key: 'buyer_schain_hop',
      dataIndex: 'buyer_schain_hop',
      width: 200,
    },
    {
      title: 'Demand Schain Complete',
      key: 'buyer_schain_complete',
      dataIndex: 'buyer_schain_complete',
      width: 220,
    },
    {
      title: 'Device Brand',
      key: 'make',
      dataIndex: 'make',
      width: 220,
      render: (txt: string) => <>{txt ? txt : '-'}</>,
    },
    {
      title: 'Device Type',
      key: 'device_type',
      dataIndex: 'device_type',
      width: 220,
      render: (device_type: number) => <>{DeviceTypeMapDesc[device_type]}</>,
    },
    {
      title: 'Bid Api',
      key: 'res_api',
      dataIndex: 'res_api',
      width: 220,
      render: (res_api: number) => (
        <>{APIFrameworksMapByValue[res_api] ? APIFrameworksMapByValue[res_api] : '-'}</>
      ),
    },
    {
      title: 'Advertiser Net Revenue',
      key: 'buyer_net_revenue',
      dataIndex: 'buyer_net_revenue',
      width: 220,
      sorter: true,
      render: (txt: string) => <>{(+txt).toFixed(2)}</>,
    },
    {
      title: 'Publisher Net Revenue',
      key: 'seller_net_revenue',
      dataIndex: 'seller_net_revenue',
      width: 200,
      // sorter: true,
      render: (txt: string) => <>{(+txt).toFixed(2)}</>,
    },
    {
      title: 'Profit',
      key: 'profit',
      dataIndex: 'profit',
      width: 150,
      // sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Request',
      key: 'request',
      dataIndex: 'request',
      width: 150,
      sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Total Request',
      key: 'total_request',
      dataIndex: 'total_request',
      sorter: true,
      width: 150,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Out Request',
      key: 'out_request',
      dataIndex: 'out_request',
      width: 150,
      render: (txt: string) => {
        return +txt ? <>{formatMoney(+txt)}</> : '-';
      },
    },
    {
      title: 'Block Request',
      key: 'block_request',
      dataIndex: 'block_request',
      width: 150,
      render: (txt: string) => {
        return +txt ? <>{formatMoney(+txt)}</> : '-';
      },
    },
    {
      title: 'Response',
      key: 'response',
      dataIndex: 'response',
      width: 150,
      // sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Win',
      key: 'win',
      dataIndex: 'win',
      width: 150,
      // sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Impression(ADM)',
      key: 'impression',
      dataIndex: 'impression',
      width: 180,
      // sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Impression(Pay)',
      key: 'seller_payment_impression',
      dataIndex: 'seller_payment_impression',
      width: 150,
      // sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Click',
      key: 'click',
      dataIndex: 'click',
      width: 150,
      // sorter: true,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Bid Floor',
      key: 'total_seller_bid_floor',
      dataIndex: 'total_seller_bid_floor',
      width: 150,
      // sorter: true,
      render: (txt: string) => <>{txt}</>,
    },
    {
      title: 'Fill Rate(%)',
      key: 'fill_rate',
      dataIndex: 'fill_rate',
      // sorter: true,
      width: 150,
    },
    {
      title: 'eCPR',
      key: 'ecpr',
      dataIndex: 'ecpr',
      sorter: true,
      width: 150,
    },
    {
      title: 'Advertiser eCPR',
      key: 'adv_ecpr',
      dataIndex: 'adv_ecpr',
      sorter: true,
      width: 150,
    },
    {
      title: 'Advertiser Gross Revenue',
      key: 'buyer_gross_revenue',
      dataIndex: 'buyer_gross_revenue',
      width: 220,
      // sorter: true,
      render: (txt: string) => <>{txt}</>,
    },
    {
      title: 'Publisher Gross Revenue',
      key: 'seller_gross_revenue',
      dataIndex: 'seller_gross_revenue',
      width: 220,
      // sorter: true,
      render: (txt: string) => <>{txt}</>,
    },
    {
      title: 'Click Rate(%)',
      key: 'click_rate',
      dataIndex: 'click_rate',
      // sorter: true,
      width: 150,
    },
    {
      title: 'CPC',
      key: 'cpc',
      dataIndex: 'cpc',
      width: 150,
      render: (txt: string) => <>{formatMoney(+txt)}</>
    },
    {
      title: 'Profit Rate(%)',
      key: 'profit_rate',
      dataIndex: 'profit_rate',
      // sorter: true,
      width: 150,
    },
    {
      title: 'Win Rate(%)',
      key: 'win_rate',
      dataIndex: 'win_rate',
      sorter: true,
      width: 150,
    },
    {
      title: 'Render Rate(%)',
      key: 'impression_rate',
      dataIndex: 'impression_rate',
      // sorter: true,
      width: 150,
    },
    {
      title: 'Advertiser Net eCPM',
      key: 'buyer_net_ecpm',
      dataIndex: 'buyer_net_ecpm',
      // sorter: true,
      width: 200,
    },
    {
      title: 'Publisher Net eCPM',
      key: 'seller_net_ecpm',
      dataIndex: 'seller_net_ecpm',
      // sorter: true,
      width: 200,
    },
    {
      title: 'Advertiser Gross eCPM',
      key: 'buyer_gross_ecpm',
      dataIndex: 'buyer_gross_ecpm',
      // sorter: true,
      width: 220,
    },
    {
      title: 'Bid Price',
      key: 'bid_price',
      dataIndex: 'bid_price',
      // sorter: true,
      width: 120,
    },
    {
      title: 'QPS (Real)',
      key: 'real_qps',
      dataIndex: 'real_qps',
      // sorter: true,
      width: 120,
    },
    {
      title: 'Advertiser QPS (Config)',
      key: 'adv_config_qps',
      dataIndex: 'adv_config_qps',

      // sorter: true,
      ellipsis: { showTitle: false },
      width: 200,
    },
    {
      title: 'Publisher QPS (Config)',
      key: 'pub_config_qps',
      dataIndex: 'pub_config_qps',

      // sorter: true,
      ellipsis: { showTitle: false },
      width: 200,
    },
    {
      title: 'Adv Req Time (ms)',
      key: 'avg_dsp_cost_time',
      dataIndex: 'avg_dsp_cost_time',
      ellipsis: { showTitle: false },
      width: 200,
      render: (time: number) => <>{time || 0}</>,
    },
    {
      title: 'Adv Resp Time (ms)',
      key: 'avg_response_cost_time',
      dataIndex: 'avg_response_cost_time',
      ellipsis: { showTitle: false },
      width: 200,
      render: (time: number) => <>{time || 0}</>,
    },
    {
      title: 'Imp Banner JS',
      key: 'imp_banner_js',
      dataIndex: 'imp_banner_js',
      ellipsis: { showTitle: false },
      width: 200,
      render: (txt: number) => <>{txt || '-'}</>,
    },
    {
      title: 'Imp Banner Pixel',
      key: 'imp_banner_pixel',
      dataIndex: 'imp_banner_pixel',
      ellipsis: { showTitle: false },
      width: 200,
      render: (txt: number) => <>{txt || '-'}</>,
    },
    {
      title: 'Imp Banner Mraid',
      key: 'imp_banner_mraid',
      dataIndex: 'imp_banner_mraid',
      ellipsis: { showTitle: false },
      width: 200,
      render: (txt: number) => <>{txt || '-'}</>,
    },
    {
      title: 'Tmax(ms)',
      key: 'tmax',
      dataIndex: 'tmax',
      ellipsis: { showTitle: false },
      width: 150,
      render: (txt: number) => <>{txt || 0}</>,
    },
    {
      title: 'PX Prebid Scan Rate(%)',
      key: 'pixalate_be_filtered_rate',
      dataIndex: 'pixalate_be_filtered_rate',
      ellipsis: { showTitle: false },
      width: 220,
      sorter: true,
      render: (txt: number) => <>{txt || 0}</>,
    },
    {
      title: 'PX Prebid Pass Rate(%)',
      key: 'pixalate_passed_filtered_rate',
      dataIndex: 'pixalate_passed_filtered_rate',
      ellipsis: { showTitle: false },
      width: 260,
      sorter: true,
      render: (txt: number) => <>{txt || 0}</>,
    },
    {
      title: 'Same IP Rate(ADM) (%)',
      key: 'same_ip_rate',
      dataIndex: 'same_ip_rate',
      ellipsis: { showTitle: false },
      width: 200,
      sorter: true,
      render: (txt: number) => <>{txt || 0}</>,
    },
    {
      title: 'Same IP Rate(Pay) (%)',
      key: 'seller_same_ip_rate',
      dataIndex: 'seller_same_ip_rate',
      ellipsis: { showTitle: false },
      width: 200,
      sorter: true,
      render: (txt: number) => <>{txt || 0}</>,
    },
  ]);

export const MetricsShortcutOptions = [
  {
    label: 'Default',
    key: 'default',
  },
  {
    label: 'Option 1',
    key: 'option1',
  },
  {
    label: 'Option 2',
    key: 'option2',
  },
];

export const MetricsShortcutOptionsMap: { [key: string]: string[] } = {
  default: [
    'buyer_net_revenue',
    'request',
    'total_request',
    'total_seller_bid_floor',
    'fill_rate',
    'ecpr',
    'click_rate',
    'profit_rate',
    'win_rate',
    'bid_price',
    'real_qps',
  ],
  option1: [
    'buyer_net_revenue',
    'request',
    'block_request',
    'total_seller_bid_floor',
    'fill_rate',
    'ecpr',
    'click_rate',
    'win_rate',
    'buyer_net_ecpm',
    'seller_net_ecpm',
  ],
  option2: [
    'buyer_net_revenue',
    'profit',
    'request',
    'block_request',
    'response',
    'win',
    'impression',
    'click',
    'fill_rate',
    'buyer_net_ecpm',
    'seller_net_ecpm',
  ],
};

// 用于展示的默认列，需要和 默认选中的筛选条件一致
export const DashboardDefaultColumnKeys = [
  ...DashboardDefaultDimension,
  ...MetricsShortcutOptionsMap['default'],
];

// 处理本地导出的数据映射问题
export const FormatExportValueMap: FormatExportValueMapType = {
  res_api: (res_api: any) => APIFrameworksMapByValue[res_api],
  device_type: (device_type: any) => DeviceTypeMapDesc[device_type],
  buyer_id: (_, row: any) => row.buyer,
  seller_id: (_, row: any) => row.seller,
};
