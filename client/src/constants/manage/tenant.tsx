/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-17 21:37:22
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-18 16:18:46
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
// import RixEngineFont from '@/components/RixEngineFont';
import { TopBarSearchItem } from '@/components/TopBar';
import { qpsList2Map } from '@/utils/qps';
// import { Button } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { StatusOptions } from '@/constants';

export const ecprLevelTypeOptions = [
  {
    label: 'Seller-Buyer',
    value: 1,
  },
];

export const ecprRegionOptions = [
  {
    label: 'USE',
    value: 1,
  },
  {
    label: 'APSE',
    value: 2,
  },
];

const baseList = [...Array(9).keys()].map((val) => (val + 1) * 100);
const QPSList = [
  '50',
  ...baseList,
  '1k',
  '1.5k',
  '2k',
  '2.5k',
  '3k',
  '3.5k',
  '4k',
  '4.5k',
  '5k',
  '5.5k',
  '6k',
  '7k',
  '8k',
  '9k',
  '10k',
  '15k',
  '20k',
  '25k',
  '30k',
  '35k',
  '40k',
  '45k',
  '50k',
  '60k',
  '70k',
  '80k',
];

const { arr: qps_arr } = qpsList2Map(QPSList);
export const QPSOptions = qps_arr;

export const TenantTypeOptions = [
  {
    label: 'Rix Tenant',
    value: 1,
  },
  {
    label: 'Topon Tenant',
    value: 2,
  },
];

export const TenantBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Manage',
    icon: 'antd-BorderOuterOutlined',
  },
  {
    name: 'Tenant Management',
  },
];

export const TenantColumnOptions: ColumnProps<TenantAPI.TenantListItem>[] = [
  {
    title: 'Tenant',
    width: 180,
    dataIndex: 'tnt_name',
    fixed: 'left',
    ellipsis: { showTitle: true },
    render: (_, row) => (
      <HoverToolTip title={_}>
        <span>
          {_}
          {`(${row.tnt_id})`}
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Token',
    key: 'token',
    width: 140,
    dataIndex: 'token',
    ellipsis: { showTitle: true },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Host Prefix',
    key: 'host_prefix',
    width: 150,
    dataIndex: 'host_prefix',
  },
  {
    title: 'CS_Domain',
    key: 'cs_domain',
    width: 180,
    dataIndex: 'cs_domain',
    ellipsis: { showTitle: false },
    render: (_) => {
      const url =
        process.env.CUR_ENV === 'prod' ? 'https://' + _ : 'http://' + _;
      return (
        <HoverToolTip title={_}>
          <a href={url} target="_blank" rel="noreferrer">
            {_}
          </a>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Private Domain',
    key: 'pv_domain',
    width: 180,
    dataIndex: 'pv_domain',
    ellipsis: { showTitle: false },
    render: (_) => {
      const url =
        process.env.CUR_ENV === 'prod' ? 'https://' + _ : 'http://' + _;
      return (
        <HoverToolTip title={_}>
          <a href={url} target="_blank" rel="noreferrer">
            {_}
          </a>
        </HoverToolTip>
      );
    },
  },
  // {
  //   title: 'Brand',
  //   key: 'brand',
  //   width: 150,
  //   dataIndex: 'brand',
  //   render: (_) => {
  //     if (_) {
  //       return (
  //         <HoverToolTip title={_}>
  //           <span>{_}</span>
  //         </HoverToolTip>
  //       );
  //     } else {
  //       return (
  //         <div style={{ display: 'flex', alignItems: 'center' }}>
  //           <RixEngineFont
  //             type="rix-edit"
  //             style={{ color: '#1890ff', fontSize: '16px', marginRight: '5px' }}
  //           ></RixEngineFont>
  //           <a href="/manage/privatization">Edit</a>
  //         </div>
  //       );
  //     }
  //   },
  // },
  {
    title: 'Email',
    key: 'email',
    width: 180,
    dataIndex: 'email',
    ellipsis: { showTitle: true },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Company',
    key: 'company',
    width: 180,
    dataIndex: 'company',
    ellipsis: { showTitle: true },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Contact',
    key: 'contact',
    width: 180,
    dataIndex: 'contact',
    ellipsis: { showTitle: true },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Phone',
    key: 'phone',
    width: 180,
    dataIndex: 'phone',
    ellipsis: { showTitle: true },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Pixalate Status',
    key: 'pl_status',
    width: 120,
    dataIndex: 'pl_status',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Human Status',
    key: 'hm_status',
    width: 120,
    dataIndex: 'hm_status',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Status',
    key: 'status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
];

export const TenantSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: [],
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: [],
    mode: 'multiple',
    options: StatusOptions,
  },
  {
    name: 'Tenant Type',
    type: 'select',
    key: 'tnt_type',
    value: [],
    mode: 'multiple',
    options: TenantTypeOptions,
  },
];

export const TenantDefaultFilterValues = {
  tnt_type: [1],
  status: [],
  tnt_id: [],
}
