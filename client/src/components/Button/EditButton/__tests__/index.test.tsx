import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import EditButton from '../index';

// Mock RixEngineFont component
vi.mock('@/components/RixEngineFont', () => ({
  default: ({ type }: { type: string }) => (
    <span data-testid="rix-icon">{type}</span>
  ),
}));

// Mock the CSS module
vi.mock('../index.less', () => ({
  default: {
    'edit-button': 'edit-button-mock-class',
  },
  'edit-button': 'edit-button-mock-class',
}));

describe('EditButton', () => {
  it('should render with default icon', () => {
    render(<EditButton />);

    const button = screen.getByRole('button');
    const icon = screen.getByTestId('rix-icon');

    expect(button).toBeInTheDocument();
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveTextContent('rix-edit');
  });

  it('should render with custom icon', () => {
    const customIcon = <span data-testid="custom-icon">Custom</span>;
    render(<EditButton icon={customIcon} />);

    const button = screen.getByRole('button');
    const customIconElement = screen.getByTestId('custom-icon');

    expect(button).toBeInTheDocument();
    expect(customIconElement).toBeInTheDocument();
    expect(customIconElement).toHaveTextContent('Custom');

    // Should not render default icon
    expect(screen.queryByTestId('rix-icon')).not.toBeInTheDocument();
  });

  it('should have correct button type', () => {
    render(<EditButton />);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('ant-btn-text');
  });

  it('should apply custom className along with default styles', () => {
    render(<EditButton className="custom-class" />);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('edit-button-mock-class');
    expect(button).toHaveClass('custom-class');
  });

  it('should handle click events', () => {
    const handleClick = vi.fn();
    render(<EditButton onClick={handleClick} />);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<EditButton disabled />);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('should pass through other button props', () => {
    render(<EditButton size="large" loading />);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('ant-btn-lg'); // Antd large size class
    expect(button).toHaveClass('ant-btn-loading'); // Antd loading class
  });

  it('should render children when provided', () => {
    render(<EditButton>Edit Text</EditButton>);

    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('Edit Text');
  });

  it('should maintain button accessibility', () => {
    render(<EditButton aria-label="Edit item" />);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Edit item');
  });
});
