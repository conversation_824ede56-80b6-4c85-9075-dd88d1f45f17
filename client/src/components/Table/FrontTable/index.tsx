/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-04 09:59:39
 * @Description:
 */
import type { FormInstance, TablePaginationConfig, TableProps } from 'antd';
import { Button } from 'antd';
import { ColumnProps } from 'antd/es/table';
import {
  RefObject,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';

import Pagination from '@/components/Pagination/NormalPagination';
import { ButtonType } from '@/components/Table/BackTable';
import TopBar, {
  type SearchResultItem,
  type TopBarRef,
  type TopBarSearchItem,
} from '@/components/TopBar';
import { useAccess, useLocation } from '@umijs/max';
import type { FormProps } from 'antd';
import { parse } from 'query-string';
import Table from '../OriginalTable';
import { filterTableDataArray } from './filter-data';
import styles from './index.less';

export type ColumnType<T> = ColumnProps<T> & {
  access?: string | string[];
};

type NormalTableProps<T, K> = TableProps<T> &
  FormProps & {
    loading: boolean;
    columns: ColumnProps<T>[];
    dataSource: T[];
    rowKey: string;
    searchFormRef?: RefObject<FormInstance<any> | undefined>;
    btnOptions?: ButtonType[];
    request?: () => void;
    pagination?: TablePaginationConfig;
    pageTitle?: string;
    searchOptions: TopBarSearchItem[];
    isFold?: boolean;
    labelWidth?: number;
    isBtnTable?: boolean;
    tabOptions?: { label: string; value: string | number }[];
    defaultTab?: string | number;
    onTabChange?: (tab: string | number) => void;
    defaultFold?: boolean;
    emptyRender?: () => JSX.Element;
    handleSearchValueChange?: (
      changeValue: any,
      allValue: any,
      isFold: boolean,
    ) => void;
    handleSearch?: (val: SearchResultItem[]) => void; // 搜索
    isBackSearch?: boolean; // 条件改变重新请求数据
    isExport?: boolean;
    handleExport?: (tableData: T[]) => void;
    handleClickBtn?: (item: ButtonType) => void;
    exportData?: {
      exportColumnKeys: string[];
    };
    allowSaveSearch?: boolean;
  };

const PageSize = 50;
const DefCurrentPage = 1;
const PageSizeOptions = [10, 50, 100, 500];

const getCurrentData = <T,>(data: T[], page: number, pageSize: number): T[] => {
  if (!data) {
    return [];
  }
  const start = (page - 1) * pageSize;
  const end = page * pageSize;
  return data.slice(start, end);
};

function NormalTable<T extends object>({
  columns,
  searchFormRef,
  dataSource,
  rowKey,
  btnOptions,
  request,
  pagination,
  pageTitle,
  searchOptions,
  isFold,
  labelWidth = 80,
  loading,
  isBtnTable = true,
  tabOptions,
  defaultFold = true,
  defaultTab = '',
  onTabChange = () => {},
  emptyRender,
  initialValues,
  handleSearchValueChange,
  isBackSearch,
  handleSearch,
  handleClickBtn,
  isExport,
  handleExport,
  exportData,
  allowSaveSearch,
  ...rest
}: NormalTableProps<T, keyof T>): JSX.Element {
  const access: any = useAccess();
  const [tableData, setTableData] = useState<T[]>(dataSource);
  const [currentPage, setCurrentPage] = useState<number>(DefCurrentPage);
  const [pageSize, setPageSize] = useState<number>(PageSize);
  const [curPageData, setCurPageData] = useState<T[]>([]);
  const [searchVal, setSearchVal] = useState<SearchResultItem[]>([]);
  const [currentRouteSearch, setCurrentRouteSearch] = useState<any>({});
  const location = useLocation();
  const { search } = location;

  const topBarRef = useRef<TopBarRef>(null);

  useImperativeHandle(
    searchFormRef,
    () => topBarRef.current?.getFormInstance(),
    [topBarRef.current],
  );

  const accessBtnOptions = useMemo(() => {
    return (
      btnOptions?.filter((v) => {
        if ((v.access && access.isButtonAccess(v.access)) || !v.access) {
          return true;
        }
        return false;
      }) || []
    );
  }, [btnOptions]);
  const accessColumns = useMemo(() => {
    return (
      columns?.filter((v: ColumnType<T>) => {
        const accessCode = v.access
          ? Array.isArray(v.access)
            ? v.access
            : [v.access]
          : [];
        return (
          !accessCode.length || accessCode.some((t) => access.isButtonAccess(t))
        );
      }) || []
    );
  }, [columns]);

  const handlePageChange = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
    setCurPageData(getCurrentData(tableData, page, pageSize));
  };

  const reset = () => {
    setCurrentPage(DefCurrentPage);
    setPageSize(PageSize);
  };

  const [flag, setFlag] = useState<boolean>(true);

  useEffect(() => {
    if (!dataSource && request) {
      request();
    } else {
      if (!isBackSearch) {
        if (flag && dataSource.length && initialValues) {
          setFlag(false);
          let tmpRouteSearch: any = { ...initialValues };
          if (search && allowSaveSearch) {
            const params = parse(decodeURI(search));
            tmpRouteSearch = { ...params };
            for (const key in tmpRouteSearch) {
              if (tmpRouteSearch.hasOwnProperty(key)) {
                const isMultiple = searchOptions.some(
                  (v) => v.key === key && v.mode === 'multiple',
                );
                if (isMultiple) {
                  tmpRouteSearch[key] = (params[key] as string)
                    ?.split(',')
                    .map((v) => {
                      return isNaN(+v) ? v : +v;
                    });
                }
              }
            }
          }
          const tmp: SearchResultItem[] = Object.keys({
            ...tmpRouteSearch,
          }).map((key) => {
            return {
              key,
              value: tmpRouteSearch[key],
              mode: Array.isArray(tmpRouteSearch[key])
                ? 'multiple'
                : typeof tmpRouteSearch[key] === 'string'
                ? 'single'
                : undefined,
            };
          });
          setCurrentRouteSearch(tmpRouteSearch);
          handleSearchChange(tmp);
        } else {
          handleSearchChange(searchVal);
        }
        reset();
      } else {
        setTableData(dataSource);
      }
    }
  }, [dataSource, isBackSearch, search]);

  useEffect(() => {
    setCurrentPage(1);
    setPageSize(PageSize);
    setCurPageData(getCurrentData(tableData, DefCurrentPage, PageSize));
  }, [tableData]);

  const handleFilter = (val: SearchResultItem[]) => {
    // 过滤空值
    const tmp = val.filter((item) => {
      if (Array.isArray(item.value)) {
        return item.value.length;
      } else {
        return item.value || +item.value === 0;
      }
    });

    // 前端过滤table的核心算法逻辑，返回过滤后的新数据
    const data = filterTableDataArray(dataSource, tmp) as T[];

    setTableData(data);
  };

  const handleSearchChange = (val: SearchResultItem[]) => {
    setSearchVal(val);
    if (isBackSearch) {
      handleSearch && handleSearch(val);
    } else {
      handleFilter(val);
    }
  };

  const handleReload = () => {
    request && request();
  };

  const handleCommonClickBtn = (item: ButtonType, e: any) => {
    item.onClick && item.onClick(e);
    handleClickBtn && handleClickBtn(item);
  };

  const handleExportData = () => {
    // 需要传入数据
    handleExport && handleExport(tableData);
  };

  return (
    <div className={styles['front-table-container']}>
      <div className={styles['front-table-header']}>
        <TopBar
          ref={topBarRef}
          title={pageTitle}
          handleSearchChange={handleSearchChange}
          handleReload={handleReload}
          isFold={isFold}
          labelWidth={labelWidth}
          loading={loading}
          isFront={true}
          tabOptions={tabOptions}
          defaultTab={defaultTab}
          onTabChange={onTabChange}
          defaultFold={defaultFold}
          searchOptions={searchOptions}
          initialValues={initialValues}
          handleSearchValueChange={handleSearchValueChange}
          isExport={isExport}
          handleExport={handleExportData}
          allowSaveSearch={allowSaveSearch}
          currentRouteSearch={currentRouteSearch}
        />
      </div>
      <div className={`${styles['bottom-container']}`}>
        <div className={styles['bottom-top']}>
          <div className={styles['top-left']}>
            {accessBtnOptions.map((item: ButtonType, index) => {
              return (
                <Button
                  {...item}
                  key={index}
                  onClick={(e) => handleCommonClickBtn(item, e)}
                >
                  {item.label}
                </Button>
              );
            })}
          </div>
          <div className={styles['top-right']}>
            <Pagination
              onChange={handlePageChange}
              total={tableData?.length || 0}
              pageSize={pageSize}
              current={currentPage}
              showSizeChanger
              size="small"
              responsive
              showTotal={(total) => `Total ${total} items`}
              pageSizeOptions={PageSizeOptions}
              {...(pagination as TablePaginationConfig)}
            />
          </div>
        </div>
        {useMemo(() => {
          return (
            <Table<T>
              {...rest}
              columns={accessColumns}
              dataSource={curPageData}
              rowKey={rowKey}
              loading={loading}
              isBtnTable={isBtnTable}
              scroll={{ y: 'calc(100vh - 220px)' }}
              emptyRender={emptyRender}
            />
          );
        }, [dataSource, curPageData, loading, columns, rowKey, isBtnTable])}
      </div>
    </div>
  );
}

export default NormalTable;
