import type { SearchResultItem } from '@/components/TopBar';
import { beforeEach, describe, expect, it } from 'vitest';
import {
  filterTableData,
  filterTableDataArray,
  handleFilterData,
} from './filter-data';

// 定义测试数据类型
interface TestDataItem {
  id: number;
  name: string;
  status: string;
  tags: string;
  type: number;
  children?: TestDataItem[];
  allChildren?: TestDataItem[];
  [key: string]: any;
}

describe('表格数据过滤函数测试', () => {
  let testData: TestDataItem;
  let testDataArray: TestDataItem[];
  let searchConditions: SearchResultItem[];

  beforeEach(() => {
    // 准备测试数据
    testData = {
      id: 1,
      name: '父级项目',
      status: 'active',
      tags: 'tag1,tag2,tag3',
      type: 1,
      children: [
        {
          id: 2,
          name: '子级项目1',
          status: 'inactive',
          tags: 'tag1,tag4',
          type: 2,
        },
        {
          id: 3,
          name: '子级项目2',
          status: 'active',
          tags: 'tag2,tag5',
          type: 1,
          children: [
            {
              id: 4,
              name: '孙级项目',
              status: 'pending',
              tags: 'tag3,tag6',
              type: 3,
            },
          ],
        },
      ],
    };

    testDataArray = [
      {
        id: 1,
        name: '测试项目A',
        status: 'active',
        tags: 'react,typescript',
        type: 1,
      },
      {
        id: 2,
        name: '测试项目B',
        status: 'inactive',
        tags: 'vue,javascript',
        type: 2,
      },
      {
        id: 3,
        name: '测试项目C',
        status: 'pending',
        tags: 'angular,typescript',
        type: 1,
      },
    ];

    // 准备搜索条件
    searchConditions = [
      {
        key: 'status',
        value: 'active',
      },
    ];
  });

  describe('filterTableData - 新的不可变过滤函数', () => {
    it('应该过滤出匹配条件的数据项', () => {
      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.id).toBe(1);
      expect(result!.status).toBe('active');
    });

    it('应该保持原始数据不变（数据不可变性）', () => {
      const originalData = JSON.parse(JSON.stringify(testData));
      const result = filterTableData(testData, searchConditions);

      // 验证原始数据完全没有改变
      expect(testData).toEqual(originalData);
      expect(testData).not.toBe(result); // 不是同一个对象引用
    });

    it('应该正确处理树形结构的递归过滤', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'name',
          value: '子级',
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.children).toBeDefined();
      expect(result!.children!.length).toBeGreaterThan(0);
    });

    it('应该在没有匹配项时返回 null', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'status',
          value: 'nonexistent',
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).toBeNull();
    });

    it('应该正确处理多选模式的精确匹配', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'tags',
          value: ['tag1', 'tag2'],
          mode: 'multiple',
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.tags).toContain('tag1');
      expect(result!.tags).toContain('tag2');
    });

    it('应该正确处理多选模式的模糊匹配', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'tags',
          value: ['tag'],
          mode: 'multiple',
          isNoExact: true,
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.tags).toMatch(/tag/);
    });

    it('应该正确处理字符串模糊匹配', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'name',
          value: '父级',
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.name).toContain('父级');
    });

    it('应该正确处理数值类型的匹配', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'type',
          value: 1,
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.type).toBe(1);
    });

    it('应该正确处理数值 0 的特殊情况', () => {
      const testDataWithZero = {
        ...testData,
        count: 0,
      };

      const searchConditions: SearchResultItem[] = [
        {
          key: 'count',
          value: 0,
        },
      ];

      const result = filterTableData(testDataWithZero, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.count).toBe(0);
    });

    it('应该正确处理空值和 undefined 的情况', () => {
      const testDataWithNull = {
        ...testData,
        description: null,
        notes: undefined,
      };

      const searchConditions: SearchResultItem[] = [
        {
          key: 'description',
          value: '',
        },
      ];

      const result = filterTableData(testDataWithNull, searchConditions);

      expect(result).not.toBeNull();
    });

    it('应该正确处理多条件组合（AND 逻辑）', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'status',
          value: 'active',
        },
        {
          key: 'type',
          value: 1,
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.status).toBe('active');
      expect(result!.type).toBe(1);
    });

    it('应该正确处理大小写不敏感的字符串匹配', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'name',
          value: '父级项目',
        },
      ];

      const result = filterTableData(testData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.name.toLowerCase()).toContain('父级项目'.toLowerCase());
    });
  });

  describe('filterTableDataArray - 批量数组过滤函数', () => {
    it('应该过滤数组中匹配条件的所有项', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'type',
          value: 1,
        },
      ];

      const result = filterTableDataArray(testDataArray, searchConditions);

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBe(2); // 有两个 type 为 1 的项
      result.forEach((item) => {
        expect(item.type).toBe(1);
      });
    });

    it('应该保持原始数组不变（数据不可变性）', () => {
      const originalArray = JSON.parse(JSON.stringify(testDataArray));
      const result = filterTableDataArray(testDataArray, searchConditions);

      expect(testDataArray).toEqual(originalArray);
      expect(testDataArray).not.toBe(result);
    });

    it('应该在没有匹配项时返回空数组', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'status',
          value: 'nonexistent',
        },
      ];

      const result = filterTableDataArray(testDataArray, searchConditions);

      expect(result).toBeInstanceOf(Array);
      expect(result).toHaveLength(0);
    });

    it('应该正确处理包含复杂搜索条件的数组过滤', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'tags',
          value: ['typescript'],
          mode: 'multiple',
        },
      ];

      const result = filterTableDataArray(testDataArray, searchConditions);

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBe(2); // 有两个包含 typescript 的项
      result.forEach((item) => {
        expect(item.tags).toContain('typescript');
      });
    });

    it('处理多选数字值的过滤逻辑', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'type',
          value: [1, 2],
          mode: 'multiple',
        },
      ];

      const result = filterTableDataArray(testDataArray, searchConditions);

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBe(3); // 有三个 type 为 1 或 2 的项
    });
  });

  describe('handleFilterData - 兼容性函数（会修改原数据）', () => {
    let testDataCopy: TestDataItem;

    beforeEach(() => {
      // 每次测试前重新复制数据，因为这个函数会修改原数据
      testDataCopy = JSON.parse(JSON.stringify(testData));
    });

    it('应该返回布尔值表示是否匹配', () => {
      const result = handleFilterData(testDataCopy, searchConditions);

      expect(typeof result).toBe('boolean');
      expect(result).toBe(true);
    });

    it('应该修改原始数据（向后兼容性）', () => {
      const originalData = JSON.parse(JSON.stringify(testDataCopy));
      const result = handleFilterData(testDataCopy, searchConditions);

      // 验证原数据确实被修改了
      expect(testDataCopy).not.toEqual(originalData);
      expect(result).toBe(true);
    });

    it('应该正确处理子级数据的过滤', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'status',
          value: 'active',
        },
      ];

      const originalChildrenCount = testDataCopy.children?.length || 0;
      const result = handleFilterData(testDataCopy, searchConditions);

      expect(result).toBe(true);
      expect(testDataCopy.children!.length).toBeLessThanOrEqual(
        originalChildrenCount,
      );
    });

    it('应该在没有匹配的子级时删除 children 属性', () => {
      const searchConditions: SearchResultItem[] = [
        {
          key: 'status',
          value: 'nonexistent',
        },
      ];

      const result = handleFilterData(testDataCopy, searchConditions);

      expect(result).toBe(false);
      expect(testDataCopy.children).toBeUndefined();
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该正确处理空的搜索条件', () => {
      const result = filterTableData(testData, []);

      expect(result).not.toBeNull();
      expect(result!.id).toBe(testData.id);
    });

    it('应该正确处理没有 children 的数据项', () => {
      const simpleData = {
        id: 1,
        name: '简单数据',
        status: 'active',
      };

      const result = filterTableData(simpleData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.name).toBe('简单数据');
    });

    it('应该正确处理深层嵌套的树形结构', () => {
      const deepNestedData = {
        id: 1,
        name: '根节点',
        status: 'active',
        children: [
          {
            id: 2,
            name: '二级节点',
            status: 'active',
            children: [
              {
                id: 3,
                name: '三级节点',
                status: 'active',
                children: [
                  {
                    id: 4,
                    name: '四级节点',
                    status: 'active',
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = filterTableData(deepNestedData, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.children).toBeDefined();
      expect(result!.children![0].children).toBeDefined();
      expect(result!.children![0].children![0].children).toBeDefined();
    });

    it('应该正确处理包含日期对象的数据', () => {
      const dataWithDate = {
        ...testData,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-02'),
      };

      const result = filterTableData(dataWithDate, searchConditions);

      expect(result).not.toBeNull();
      expect(result!.createdAt).toBeInstanceOf(Date);
      expect(result!.updatedAt).toBeInstanceOf(Date);
      // 验证日期对象是新的实例
      expect(result!.createdAt).not.toBe(dataWithDate.createdAt);
    });

    it('应该正确处理包含函数的数据（函数应该被忽略）', () => {
      const dataWithFunction = {
        ...testData,
        someFunction: () => 'test',
        anotherMethod: function () {
          return 'another';
        },
      };

      const result = filterTableData(dataWithFunction, searchConditions);

      expect(result).not.toBeNull();
      // 函数应该被深拷贝忽略，但不影响过滤结果
      expect(typeof result!.someFunction).toBe('function');
    });

    it('应该正确处理空字符串和空白字符', () => {
      const dataWithEmptyStrings = {
        ...testData,
        emptyString: '',
        whitespace: '   ',
        nullValue: null,
      };

      const searchConditions: SearchResultItem[] = [
        {
          key: 'emptyString',
          value: '',
        },
      ];

      const result = filterTableData(dataWithEmptyStrings, searchConditions);

      expect(result).not.toBeNull();
    });
  });

  describe('性能和内存测试', () => {
    it('应该能够处理大量数据而不出现性能问题', () => {
      const largeDataArray = Array.from({ length: 1000 }, (_, index) => ({
        id: index,
        name: `项目${index}`,
        status: index % 2 === 0 ? 'active' : 'inactive',
        type: index % 3,
      }));

      const start = performance.now();
      const result = filterTableDataArray(largeDataArray, searchConditions);
      const end = performance.now();

      expect(result).toBeInstanceOf(Array);
      expect(end - start).toBeLessThan(1000); // 应该在1秒内完成
    });

    it('应该正确释放内存，不产生内存泄漏', () => {
      const testDataWithCircularRef = {
        ...testData,
      };

      // 不创建循环引用，因为我们的深拷贝函数不处理循环引用
      const result = filterTableData(testDataWithCircularRef, searchConditions);

      expect(result).not.toBeNull();
    });
  });
});
