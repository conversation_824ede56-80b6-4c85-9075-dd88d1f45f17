import type { SearchResultItem } from '@/components/TopBar';
import type { FormProps } from 'antd';

/**
 * 表格数据项接口定义
 */
interface TableDataItem {
  /** 当前项的所有子级数据（包含递归子级） */
  allChildren?: TableDataItem[];
  /** 当前项的直接子级数据 */
  children?: TableDataItem[];
  /** 动态属性，支持任意字段 */
  [key: string]: any;
}

/**
 * 过滤条件接口定义
 */
interface FilterCondition {
  /** 要匹配的字段名 */
  key: string;
  /** 要匹配的值 */
  value: any;
  /** 匹配模式：single-单选，multiple-多选 */
  mode?: 'single' | 'multiple';
  /** 是否模糊匹配（非精确匹配） */
  isNoExact?: boolean;
}

/**
 * 轻量级深拷贝实现，专门用于表格数据结构
 * 支持基本数据类型、数组、对象，忽略函数、Symbol等复杂类型
 * @param obj - 要拷贝的对象
 * @returns 深拷贝后的新对象
 */
function deepCloneTableData<T>(obj: T): T {
  // 处理基本类型和 null
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 处理日期对象
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map((item) => deepCloneTableData(item)) as T;
  }

  // 处理普通对象
  const clonedObj = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clonedObj[key] = deepCloneTableData(obj[key]);
    }
  }

  return clonedObj;
}

/**
 * 检查数组是否有有效元素
 * @param arr - 要检查的数组
 * @returns 是否有有效元素
 */
function hasValidElements(arr: unknown): arr is any[] {
  return Array.isArray(arr) && arr.length > 0;
}

/**
 * 规范化字段值，处理特殊类型转换
 * @param value - 原始字段值
 * @returns 规范化后的值
 */
function normalizeFieldValue(value: any): any {
  // 处理数值 0 的特殊情况
  if (value === 0) {
    return 0;
  }
  // 空值统一转换为空字符串
  if (!value) {
    return '';
  }
  return value;
}

/**
 * 将字符串值转换为数组格式
 * @param value - 要转换的值
 * @returns 字符串数组，去除空白项
 */
function convertToStringArray(value: any): string[] {
  return typeof value === 'string'
    ? value.split(',').filter((item) => item.trim())
    : [];
}

/**
 * 执行多选模式的匹配逻辑
 * @param fieldStringArray - 字段值转换的字符串数组
 * @param searchValueArray - 搜索条件值数组
 * @param originalValue - 原始字段值
 * @param isNoExact - 是否模糊匹配
 * @returns 是否匹配成功
 */
function matchMultipleMode(
  fieldStringArray: string[],
  searchValueArray: string[],
  originalValue: any,
  isNoExact: boolean,
): boolean {
  // 模糊匹配：字段值中任一项包含搜索值中的任一项
  if (isNoExact) {
    return fieldStringArray.some((fieldItem) =>
      searchValueArray.some((searchItem) => fieldItem.includes(searchItem)),
    );
  }

  // 精确匹配：如果字段值有数组形式，检查是否有交集
  if (fieldStringArray.length > 0) {
    return fieldStringArray.some((fieldItem) =>
      searchValueArray.includes(fieldItem),
    );
  }

  // 直接值匹配：检查原始值是否在搜索数组中
  return searchValueArray.includes(originalValue);
}

/**
 * 执行字符串匹配逻辑（忽略大小写）
 * @param value - 要匹配的值
 * @param searchValue - 搜索值
 * @returns 是否匹配成功
 */
function matchStringValue(value: any, searchValue: any): boolean {
  const stringValue = String(value).toLowerCase();
  const stringSearchValue = String(searchValue).toLowerCase();
  return stringValue.indexOf(stringSearchValue) !== -1;
}

/**
 * 检查单个搜索条件是否匹配数据项
 * @param dataItem - 数据项
 * @param condition - 搜索条件
 * @returns 是否匹配成功
 */
function matchSingleCondition(
  dataItem: TableDataItem,
  condition: FilterCondition,
): boolean {
  // 获取并规范化字段值
  const normalizedValue = normalizeFieldValue(dataItem[condition.key]);

  // 转换为字符串数组用于多选匹配
  const fieldStringArray = convertToStringArray(normalizedValue);

  // 多选模式匹配
  if (Array.isArray(condition.value) && condition.mode === 'multiple') {
    const searchValueArray = condition.value.map((item) => String(item));
    return matchMultipleMode(
      fieldStringArray,
      searchValueArray,
      normalizedValue,
      Boolean(condition.isNoExact),
    );
  }

  // 字符串模糊匹配
  if (typeof normalizedValue === 'string' && normalizedValue) {
    return matchStringValue(normalizedValue, condition.value);
  }

  // 精确值匹配（使用宽松相等比较）
  // eslint-disable-next-line eqeqeq
  return normalizedValue == condition.value;
}

/**
 * 处理子级数据的过滤逻辑（返回新的数据项，不修改原数据）
 * @param dataItem - 数据项
 * @param searchConditions - 搜索条件数组
 * @returns 处理后的新数据项
 */
function processChildrenData(
  dataItem: TableDataItem,
  searchConditions: FilterCondition[],
): TableDataItem {
  // 创建数据项的浅拷贝，为了后续修改 children 相关属性
  const processedItem = { ...dataItem };

  const hasAllChildren = hasValidElements(dataItem.allChildren);
  const hasDirectChildren = hasValidElements(dataItem.children);

  let childrenToProcess: TableDataItem[] = [];

  if (hasAllChildren) {
    // 如果有 allChildren，使用它作为数据源，并更新 children
    childrenToProcess = dataItem.allChildren!;
    const filteredChildren = childrenToProcess
      .map((child) => filterTableDataInternal(child, searchConditions))
      .filter((child) => child !== null) as TableDataItem[];

    processedItem.children =
      filteredChildren.length > 0 ? filteredChildren : undefined;
  } else {
    // 备份 children 到 allChildren，然后过滤 children
    processedItem.allChildren = dataItem.children;
    childrenToProcess = dataItem.children || [];

    if (hasDirectChildren) {
      const filteredChildren = childrenToProcess
        .map((child) => filterTableDataInternal(child, searchConditions))
        .filter((child) => child !== null) as TableDataItem[];

      processedItem.children =
        filteredChildren.length > 0 ? filteredChildren : undefined;
    }
  }

  return processedItem;
}

/**
 * 内部过滤函数，处理数据项并返回过滤后的新数据项或 null
 * @param dataItem - 要过滤的数据项
 * @param searchConditions - 搜索条件数组
 * @returns 过滤后的新数据项，如果不匹配则返回 null
 */
function filterTableDataInternal(
  dataItem: TableDataItem,
  searchConditions: FilterCondition[],
): TableDataItem | null {
  // 处理子级数据的过滤（返回新的数据项）
  const processedItem = processChildrenData(dataItem, searchConditions);

  // 检查当前数据项是否满足所有搜索条件（AND 逻辑）
  const isCurrentItemMatched = searchConditions.every((condition) =>
    matchSingleCondition(dataItem, condition),
  );

  // 检查是否有匹配的子级数据
  const hasMatchedChildren = hasValidElements(processedItem.children);

  // 如果当前项匹配或有匹配的子级，返回处理后的数据项
  if (isCurrentItemMatched || hasMatchedChildren) {
    return processedItem;
  }

  // 都不匹配则返回 null
  return null;
}

/**
 * 过滤表格数据，支持树形结构和多种匹配模式（不可变版本）
 *
 * 主要功能：
 * 1. 支持树形数据结构的递归过滤
 * 2. 支持多种匹配模式：精确匹配、模糊匹配、多选匹配
 * 3. 保持数据结构完整性，支持 allChildren 和 children 的自动管理
 * 4. 数据不可变性：不修改原始数据，返回过滤后的新数据
 * 5. 优化性能，避免重复计算
 *
 * 使用场景：
 * - 表格数据的前端过滤
 * - 树形选择器的数据过滤
 * - 搜索结果的实时筛选
 *
 * @param dataItem - 要过滤的数据项，支持树形结构（原数据不会被修改）
 * @param searchConditions - 搜索条件数组，支持多条件组合
 * @returns 过滤后的新数据项，如果不匹配则返回 null
 */
export function filterTableData(
  dataItem: TableDataItem,
  searchConditions: FilterCondition[],
): TableDataItem | null {
  // 对输入数据进行深拷贝，确保不修改原始数据
  const clonedDataItem = deepCloneTableData(dataItem);

  // 使用内部函数处理克隆后的数据
  return filterTableDataInternal(clonedDataItem, searchConditions);
}

/**
 * 过滤表格数据数组，返回过滤后的新数组
 * @param dataArray - 要过滤的数据数组
 * @param searchConditions - 搜索条件数组
 * @returns 过滤后的新数据数组
 */
export function filterTableDataArray(
  dataArray: TableDataItem[],
  searchConditions: FilterCondition[],
): TableDataItem[] {
  return dataArray
    .map((item) => filterTableData(item, searchConditions))
    .filter((item) => item !== null) as TableDataItem[];
}

/**
 * 处理过滤数据 - 兼容性函数（保持原有行为，会修改传入的数据）
 * @deprecated 请使用 filterTableData 函数，新函数支持数据不可变性
 * @param item - 数据项（会被修改）
 * @param searchVal - 搜索条件数组
 * @returns 数据项是否匹配搜索条件
 */
export function handleFilterData(
  item: any,
  searchVal: SearchResultItem[],
): boolean {
  // 为了保持兼容性，这里仍然直接修改传入的 item
  // 但内部使用新的过滤逻辑来判断是否匹配

  const hasAllChildren = hasValidElements(item.allChildren);
  const hasDirectChildren = hasValidElements(item.children);

  let childrenToProcess: any[] = [];

  if (hasAllChildren) {
    // 如果有 allChildren，使用它作为数据源，并更新 children
    childrenToProcess = item.allChildren;
    item.children = childrenToProcess.filter((childItem) =>
      handleFilterData(childItem, searchVal),
    );
  } else {
    // 备份 children 到 allChildren，然后过滤 children
    item.allChildren = item.children;
    childrenToProcess = item.children || [];

    if (hasDirectChildren) {
      item.children = childrenToProcess.filter((childItem) =>
        handleFilterData(childItem, searchVal),
      );
    }
  }

  // 如果过滤后没有子级数据，删除 children 属性
  if (!item.children?.length) {
    delete (item as FormProps).children;
  }

  // 检查当前数据项是否满足所有搜索条件
  const isMatched = searchVal.every((condition) =>
    matchSingleCondition(item, condition),
  );

  return isMatched;
}

/**
 * 使用示例：
 *
 * // 推荐使用新的不可变函数
 * const originalData = { id: 1, name: 'test', children: [...] };
 * const filteredData = filterTableData(originalData, searchConditions);
 * // originalData 保持不变，filteredData 是新的对象
 *
 * // 批量过滤数组数据
 * const originalArray = [{ id: 1, name: 'test1' }, { id: 2, name: 'test2' }];
 * const filteredArray = filterTableDataArray(originalArray, searchConditions);
 * // originalArray 保持不变，filteredArray 是新的数组
 *
 * // 兼容性使用（会修改原数据，不推荐）
 * const isMatched = handleFilterData(dataItem, searchConditions);
 */
