import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalInput from '@/components/Input/NormalInput';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import NormalSelect from '@/components/Select/NormalSelect';
import FrontTable from '@/components/Table/FrontTable';
import { SearchResultItem } from '@/components/TopBar';
import {
  AppCrawlerBaseColumns,
  AppCrawlerBreadOptions,
  AppCrawlerSearchOption,
  PlatformOptions,
} from '@/constants/transparency/app-crawler';
import useCustomRequest from '@/hooks/useCustomRequest';
import {
  batchCreateAppCrawler,
  getAppCrawlerList,
  updateAppCrawler,
} from '@/services/transparency';
import { fetchData } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { Form, message } from 'antd';
import { ColumnProps } from 'antd/es/table';
import { useEffect, useMemo, useRef, useState } from 'react';
import BatchCreateAppCrawler from './BatchCreateAppCrawler';

const AppCrawler = () => {
  // 更新和新增 弹窗
  const [appCrawlerModalVisible, setAppCrawlerModalVisible] = useState(false);
  // 判断表单是否编辑
  const [isEdit, setIsEdit] = useState(false);
  // 保存筛选项
  const searchValuesRef = useRef<any>({});
  // 表单数据
  const [form] = Form.useForm();
  // 获取列表数据 接口
  const { run, loading, data } = useCustomRequest(getAppCrawlerList);
  const [editLoading, setEditLoading] = useState(false);
  // 处理过滤值
  const handleSearch = (items: SearchResultItem[]) => {
    // 移除没有数据的选项
    const newValues = items
      .filter((item) => item.value !== undefined)
      .reduce((acc, item) => {
        acc[item.key] = item.value;
        return acc;
      }, {} as any);
    searchValuesRef.current = newValues;
    run(newValues);
  };
  // 弹窗处理事件
  const handleAppCrawlerModal = (visible: boolean, item: any = null) => {
    setAppCrawlerModalVisible(visible);
    if (!visible || !item) {
      form.resetFields();
      form.setFieldsValue({});
      setIsEdit(false);
      return;
    }

    form.setFieldsValue(item ? { ...item } : {});
    setIsEdit(!!item);
  };
  // 处理创建和编辑的表单数据
  const handleEditForm = async () => {
    const values = await form.validateFields();
    // 移除没有数据，转换数据格式
    const newParams = Object.keys(values).reduce((acc, key) => {
      if (values[key] !== undefined) {
        acc[key] = values[key];
      }
      return acc;
    }, {} as any);
    const requestFunc = isEdit ? updateAppCrawler : batchCreateAppCrawler;

    fetchData({
      setLoading: setEditLoading,
      request: requestFunc,
      params: newParams,
      onSuccess: () => {
        message.success(isEdit ? 'Update Success' : 'Create Success');
        setAppCrawlerModalVisible(false);
        run(searchValuesRef.current);
      },
      onError: () => {
        message.error(isEdit ? 'Update Failed' : 'Create Failed');
        setAppCrawlerModalVisible(false);
      },
    });
  };

  const AppCrawlerColumns = useMemo<ColumnProps<any>[]>(() => {
    return [
      ...AppCrawlerBaseColumns,
      genOperateColumn<any>({
        width: 120,
        btnOptions: [
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            onClick: (row) => handleAppCrawlerModal(true, row),
          },
        ],
      }),
    ];
  }, []);
  // 初始化请求列表数据
  useEffect(() => {
    run();
  }, []);
  return (
    <PageContainer options={AppCrawlerBreadOptions}>
      <FrontTable<any>
        rowKey="app_id"
        pageTitle="App Crawler"
        searchOptions={AppCrawlerSearchOption}
        columns={AppCrawlerColumns}
        isFold
        defaultFold
        scroll={{ x: 1000, y: 'auto' }}
        labelWidth={150}
        handleSearch={handleSearch}
        isBackSearch
        dataSource={data}
        loading={loading}
        btnOptions={[
          {
            label: 'Add App Crawler',
            type: 'primary',
            size: 'small',
            onClick: () => handleAppCrawlerModal(true),
            icon: <RixEngineFont type="add" />,
          },
        ]}
      />
      <NormalDrawer
        blackName={`${isEdit ? 'Edit' : 'Add'} App Crawler`}
        open={appCrawlerModalVisible}
        onConfirm={handleEditForm}
        loading={editLoading}
        onClose={() => handleAppCrawlerModal(false)}
        width="min(100%, 420px)"
        maskClosable
      >
        <Form form={form} layout="vertical">
          {isEdit ? (
            <>
              <Form.Item name="app_id" hidden />
              <Form.Item
                name="app_bundle_id"
                label="App Bundle ID"
                required
                rules={[
                  { required: true, message: 'Please enter App Bundle ID' },
                ]}
              >
                <NormalInput placeholder="Please enter App Bundle ID" />
              </Form.Item>
              <Form.Item
                name="platform"
                label="Platform"
                required
                rules={[{ required: true, message: 'Please select platform' }]}
              >
                <NormalSelect
                  options={PlatformOptions}
                  placeholder="Please select platform"
                />
              </Form.Item>
            </>
          ) : (
            <Form.Item
              name="bundles"
              label="Bundles"
              rules={[{ required: true, message: 'Please enter bundles' }]}
            >
              <BatchCreateAppCrawler />
            </Form.Item>
          )}
        </Form>
      </NormalDrawer>
    </PageContainer>
  );
};

export default AppCrawler;
