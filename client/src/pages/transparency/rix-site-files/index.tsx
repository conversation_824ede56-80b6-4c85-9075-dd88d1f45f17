import NormalModal from '@/components/Modal/NormalModal';
import PageContainer from '@/components/RightPageContainer';
import Table from '@/components/Table/OriginalTable';
import {
  MenuBreadOptions,
  RixFiles,
  RixSiteColumns,
} from '@/constants/transparency/rix-site-files';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { CloudUploadOutlined, InboxOutlined } from '@ant-design/icons';
import { Card, Form, Upload, message } from 'antd';
import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import { useMemo, useState } from 'react';
import styles from './index.module.less';

const { Dragger } = Upload;

const RixSiteFiles = () => {
  const [currentFileType, setCurrentFileType] = useState<string | null>(null);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  const columns = useMemo(() => {
    return [
      ...RixSiteColumns,
      genOperateColumn<any>({
        width: 150,
        btnOptions: [
          {
            label: 'Upload',
            onClick: (params) => {
              setCurrentFileType(params.id);
              setFileList([]);
              setOpenModal(true);
            },
            icon: <CloudUploadOutlined />,
          },
        ],
      }),
    ];
  }, []);

  const uploadProps = {
    name: 'file',
    multiple: false,
    onRemove: (file: UploadFile) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file: RcFile, FileList: RcFile[]) => {
      const validTypes: { [key: string]: string[] } = {
        'sellers.json': ['application/json'],
        'ads.txt': ['text/plain'],
        'app-ads.txt': ['text/plain'],
      };

      if (!validTypes[file.name]?.includes(file.type)) {
        message.error(`Invalid file type for ${file.name}`);
        return false;
      }

      let uploadFileList = [...FileList];
      const expectedFileName = currentFileType?.toLowerCase();

      if (!expectedFileName) {
        message.error('Invalid file type');
        return false;
      }

      if (file.name.toLowerCase() !== expectedFileName) {
        message.error(`Please name the file as: ${expectedFileName}`);
        uploadFileList = uploadFileList.filter((item) => item.uid !== file.uid);
      }

      setFileList([...uploadFileList]);
      return false;
    },
    fileList,
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error('Please select a file first');
      return;
    }

    try {
      setUploading(true);
      const formData = new FormData();
      formData.set('file', fileList[0] as RcFile);

      const response = await fetch('/api/rix-site-files/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      if (response.ok && result.code === 0) {
        message.success(
          'Upload successful, wait for about a minute to refresh the file content',
        );
        setFileList([]);
        setOpenModal(false);
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      message.error(`Upload failed: ${error}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <PageContainer options={MenuBreadOptions} style={{ maxHeight: '100%' }}>
      <Card
        bordered
        style={{ margin: '0 12px', borderRadius: '8px' }}
        bodyStyle={{ padding: '0' }}
      >
        <div className={styles.container}>
          <div className={styles.tableHeader}>
            <h2>RixEngine WebSite Files</h2>
          </div>
          <Table
            dataSource={RixFiles}
            loading={false}
            columns={columns}
            rowKey="id"
            pagination={false}
          />
        </div>
      </Card>
      <NormalModal
        open={openModal}
        onCancel={() => {
          setOpenModal(false);
          setFileList([]);
        }}
        title={`Upload ${currentFileType}`}
        width={600}
        okText="Upload"
        onOk={handleUpload}
        confirmLoading={uploading}
      >
        <Form.Item
          label="Upload File"
          rules={[{ required: true, message: 'Please select a file' }]}
        >
          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              Click or drag file to this area to upload
            </p>
            <p className="ant-upload-hint">
              Only supports uploading one file at a time. Please ensure the file
              name matches exactly.
            </p>
          </Dragger>
        </Form.Item>
      </NormalModal>
    </PageContainer>
  );
};

export default RixSiteFiles;
