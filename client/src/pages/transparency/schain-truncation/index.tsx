import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import { TopBarSearchItem } from '@/components/TopBar';
import { StatusMap } from '@/constants';
import {
  SchainTruncationBreadOptions,
  SchainTruncationColumns,
  SchainTruncationSearchOption,
} from '@/constants/transparency/schain-truncation';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { PlusOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { useEffect, useMemo, useState } from 'react';
import SchainTruncationDrawer from './schain-drawer';

const Page = () => {
  // 获取 tenant 列表
  const { tenantList, reload: reloadTenant } = useModel('useTenantList');
  // 获取 demand 列表
  const { allDemandList, reload: reloadDemand } = useModel('useAllDemandList');
  // 获取 schain truncation 列表
  const {
    schainTruncationList,
    reload: reloadSchainTruncation,
    loading,
  } = useModel('useSchainTruncation');

  useEffect(() => {
    reloadSchainTruncation();
  }, []);

  useEffect(() => {
    if (!tenantList?.length) {
      reloadTenant();
    }
    if (!allDemandList?.length) {
      reloadDemand();
    }
  }, []);

  const tenantOptions = useMemo(() => {
    return (
      tenantList?.map((item) => ({
        label: `${item.tnt_name}(${item.tnt_id})`,
        value: item.tnt_id,
      })) || []
    );
  }, [tenantList]);

  const demandOptions = useMemo(() => {
    return (
      allDemandList?.map((item) => ({
        label: `${item.buyer_name}(${item.buyer_id})`,
        value: item.buyer_id,
        tnt_id: item.tnt_id,
      })) || []
    );
  }, [allDemandList]);

  const searchOptions = useMemo(() => {
    return SchainTruncationSearchOption.map(({ key, ...rest }) => {
      if (key === 'tnt_id') {
        return {
          ...rest,
          key,
          options: tenantOptions,
        };
      }
      if (key === 'buyer_id') {
        return {
          ...rest,
          key,
          options: demandOptions,
        };
      }
      return { ...rest, key };
    }) as TopBarSearchItem[];
  }, [tenantOptions, demandOptions]);

  // 创建 & 编辑逻辑
  const [visible, setVisible] = useState(false);
  const [currentRow, setCurrentRow] = useState<TransparencyAPI.SchainTruncationItem | null>(null);

  const handleClose = () => {
    setVisible(false);
    setCurrentRow(null);
  };

  const handleAddSchainTruncation = () => {
    setVisible(true);
    setCurrentRow(null);
  };

  const columnsWithOperation = useMemo(() => {
    const handleEdit = (row: TransparencyAPI.SchainTruncationItem) => {
      setVisible(true);
      setCurrentRow(row);
    };

    return [
      ...SchainTruncationColumns,
      genOperateColumn<TransparencyAPI.SchainTruncationColumns>({
        access: 'EditSchainTruncationCode',
        btnOptions: [
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            onClick: handleEdit,
          },
        ],
      }),
    ];
  }, []);

  return (
    <PageContainer options={SchainTruncationBreadOptions}>
      <FrontTable<TransparencyAPI.SchainTruncationColumns[]>
        pageTitle="Schain Truncation"
        searchOptions={searchOptions}
        loading={loading}
        columns={columnsWithOperation}
        dataSource={schainTruncationList}
        rowKey={'id'}
        request={reloadSchainTruncation}
        btnOptions={[
          {
            label: 'Add Schain Truncation',
            type: 'primary',
            icon: <PlusOutlined />,
            size: 'small',
            onClick: handleAddSchainTruncation,
            access: 'AddSchainTruncationCode',
          },
        ]}
        initialValues={{ status: [StatusMap.Active] }}
        labelWidth={80}
        scroll={{ x: 1000, y: 'auto' }}
        isFold={true}
      />
      <SchainTruncationDrawer
        visible={visible}
        currentRow={currentRow}
        handleClose={handleClose}
        refresh={reloadSchainTruncation}
        tenantOptions={tenantOptions}
        demandOptions={demandOptions}
      />
    </PageContainer>
  );
};

export default Page;
