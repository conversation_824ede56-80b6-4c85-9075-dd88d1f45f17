/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-19 18:57:37
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:39:53
 * @Description:
 */
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import type { SearchResultItem } from '@/components/TopBar';
import {
  AppInfoBreadOptions,
  AppInfoColumns,
  AppInfoSearchOption,
  SellerColumns,
  SellersJsonFields,
} from '@/constants/transparency/app-info';
import { getTransparencyAppInfo, updateAppInfoTag } from '@/services/api';
import { downloadCsv, fetchData } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import { message, Switch } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import EditAppInfo from '../components/EditAppInfo';

const Page: React.FC = () => {
  const [dataSource, setDataSource] = useState<TransparencyAPI.AppInfoItem[]>(
    [],
  );
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [visible, setVisible] = useState(false);
  const [editItem, setEditItem] = useState<TransparencyAPI.AppInfoItem>();
  const searchValueRef = useRef<SearchResultItem[]>([]);
  const [searchOptions, setSearchOptions] = useState(AppInfoSearchOption);
  const [loadingStatusId, setLoadingStatusId] = useState(0);
  const { options: supplyEPOptions, fetchSupplyEP } = useModel('useSupplyEP');
  // 使用 useState 保存 supply_tag 是否存在值的状态
  const [supplyTagFull, setSupplyTagFull] = useState(false);

  useEffect(() => {
    if (supplyEPOptions.length === 0) {
      fetchSupplyEP();
    }
  }, []);

  useEffect(() => {
    // 更新 supply_tag 的 选项
    const tmp = searchOptions.map((item) => {
      if (item.key === 'supply_tag') {
        return {
          ...item,
          options: supplyEPOptions,
        };
      }
      return item;
    });
    setSearchOptions(tmp);
  }, [supplyEPOptions]);

  const handleRefreshTag = (row: TransparencyAPI.AppInfoItem) => {
    setLoadingStatusId(row.app_id);
    fetchData({
      request: updateAppInfoTag,
      params: {
        app_id: row.app_id,
        update_tag: row.update_tag === 1 ? 0 : 1,
      },
      onSuccess() {
        message.success('Refresh successfully');
        getTableData(searchValueRef.current);
      },
      onError(e) {
        message.error(e.message);
      },
      onFinally() {
        setLoadingStatusId(0);
      },
    });
  };

  const handleEdit = (row: TransparencyAPI.AppInfoItem) => {
    setIsEdit(true);
    setVisible(true);
    setEditItem(row);
  };

  const columns = useMemo(() => {
    // 创建一个映射表, key 为 dataIndex, value 为 render 函数
    const customRenders: Record<
      string,
      (status: number, row: TransparencyAPI.AppInfoItem) => React.ReactNode
    > = {
      update_tag: (status: number, row: TransparencyAPI.AppInfoItem) => (
        <Switch
          checked={status === 1}
          onChange={(e) => handleRefreshTag(row)}
          loading={loadingStatusId === row.app_id}
          checkedChildren="Active"
          unCheckedChildren="Paused"
        />
      ),
      // 可以在这里添加更多 dataIndex 和对应的 render 函数
    };

    let tempColumns = AppInfoColumns.map((column: any) => ({
      ...column,
      render: customRenders[column.dataIndex] || column.render,
    }));

    // 添加使用 genOperateColumn 重构的操作列
    tempColumns.push(
      genOperateColumn<TransparencyAPI.AppInfoItem>({
        width: 120,
        btnOptions: [
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            onClick: (row) => handleEdit(row),
          },
        ],
      }),
    );

    if (supplyTagFull) {
      // 放到 update_tag 前面
      tempColumns.splice(
        tempColumns.findIndex((v) => v.dataIndex === 'update_tag'),
        0,
        ...SellerColumns,
      );
    }

    return tempColumns;
  }, [supplyTagFull, loadingStatusId]);

  const handleSearch = (val: SearchResultItem[]) => {
    const params: any = {};
    const tmp = val.filter(
      (item) =>
        (Array.isArray(item.value) && item.value.length > 0) ||
        (!Array.isArray(item.value) && item.value),
    );
    const bIndex = tmp.findIndex(
      (v) => v.key === 'app_bundle_id' || v.key === 'aat_domain',
    );
    if (bIndex === -1) {
      return message.error(
        'One of APP Bundle ID, AAT Domain and Supply Tag must be entered',
      );
    }

    tmp.forEach((item) => {
      params[item.key] = item.value;
    });
    getTableData(params);
    searchValueRef.current = params;
  };

  const onSuccess = (data: TransparencyAPI.AppInfoItem[]) => {
    const result = data.map((item) => {
      if (item.sellers) {
        item.sellers.forEach((seller) => {
          Object.entries(seller).forEach(([key, value]) => {
            // 如果该key还不存在于item中，则创建该key并赋值
            if (!item.hasOwnProperty(key)) {
              item[key as keyof typeof item] = [] as never;
            }
            (item[key as keyof typeof item] as any[])?.push(value);
          });
        });
      }
      return item;
    });

    setDataSource(result);
  };

  const getTableData = (params: any) => {
    fetchData({
      request: getTransparencyAppInfo,
      params,
      onSuccess,
      setLoading,
    });
  };

  const handleExport = (tableData: TransparencyAPI.AppInfoItem[]) => {
    // 打平 sellers 数据并处理字段
    const flatDataSource = tableData.reduce((acc, item) => {
      const newItem = {
        ...item,
        support_aat: item.support_aat === 1 ? 'Yes' : 'No',
        update_tag: item.update_tag === 1 ? 'Active' : 'Paused',
      };

      if (item.sellers) {
        // 如果 sellers 存在，处理 sellers 数据
        const sellersData = item.sellers.length
          ? item.sellers.map((seller) => ({
              ...newItem,
              ...seller,
            }))
          : [newItem]; // sellers 为空时，仅包含 newItem

        acc.push(...sellersData);
      } else {
        // 如果 sellers 不存在，直接添加 newItem
        acc.push(newItem);
      }

      return acc;
    }, [] as any[]);

    const fields: any = columns
      .map((item) => {
        return {
          label: typeof item.title === 'string' ? item.title : item.key,
          value: item.dataIndex,
        };
      })
      .filter((v) => v.value !== 'operate');
    const fileName = `app_info_${Date.now()}`;
    downloadCsv(fileName, flatDataSource, { fields });
  };
  const handleReload = () => {
    getTableData(searchValueRef.current);
  };

  const handleClose = () => {
    setVisible(false);
    setIsEdit(false);
  };
  const handleSearchValueChange = (
    val: TransparencyAPI.AppInfoItem,
    allVal: TransparencyAPI.AppInfoItem,
  ) => {
    const { aat_domain, app_bundle_id, supply_tag } = allVal;

    const key = Object.keys(val)[0];
    if (key === 'supply_tag' && !(supplyTagFull && supply_tag)) {
      // supplyTagFull 的变化会触发 useMemo 重新计算
      setSupplyTagFull(!!supply_tag);
    }

    // 1. 当没有选中值的时候，筛选项 aat_domain 和 app_bundle_id 必选
    // 2. 当筛选项 aat_domain 或 app_bundle_id 存在值的时候，取消必选
    const isAatDomainOrAppBundleIdSelected =
      aat_domain?.length || app_bundle_id?.length;

    // 更新 searchOptions，按照条件设置必选项
    const updatedSearchOptions = searchOptions.map((item) => {
      if (item.key === 'aat_domain' || item.key === 'app_bundle_id') {
        return {
          ...item,
          required: !isAatDomainOrAppBundleIdSelected, // 只有没有选中值时必选
        };
      }
      return item; // 对其他项不做改变
    });

    // 更新 searchOptions 状态
    setSearchOptions(updatedSearchOptions);
  };

  // 导出 sellers.json 文件
  const handleExportSellers = () => {
    if (dataSource.some((v) => !v.sellers)) {
      return message.error('No sellers found');
    }
    // 平展 sellers 数据
    const flatDataSource = dataSource.reduce(
      (acc, { sellers, app_bundle_id, aat_domain, multi_aat_domain }) => {
        if (sellers) {
          const sellersData = sellers.length
            ? sellers.map((seller) => ({
                app_bundle_id,
                aat_domain,
                multi_aat_domain,
                ...seller,
              }))
            : [{ app_bundle_id, aat_domain, multi_aat_domain }];

          acc.push(...sellersData);
        }
        return acc;
      },
      [] as any[],
    );

    const fileName = `sellers_${Date.now()}`;
    downloadCsv(fileName, flatDataSource, { fields: SellersJsonFields });
  };
  return (
    <PageContainer options={AppInfoBreadOptions}>
      <FrontTable<TransparencyAPI.AppInfoItem>
        pageTitle="App Info"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'app_id'}
        isFold
        isExport
        defaultFold
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        labelWidth={150}
        handleSearch={handleSearch}
        isBackSearch
        dataSource={dataSource}
        loading={loading}
        handleExport={handleExport}
        handleSearchValueChange={handleSearchValueChange}
        btnOptions={[
          {
            label: 'Export Sellers CSV File',
            icon: <RixEngineFont type="download" />,
            type: 'primary',
            disabled:
              dataSource.length === 0 || !dataSource.some((v) => v.sellers),
            onClick: handleExportSellers,
          },
        ]}
      />
      <EditAppInfo
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        reloadInfo={handleReload}
        handleClose={handleClose}
      />
    </PageContainer>
  );
};

export default Page;
