/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-10 10:45:24
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-14 16:47:54
 * @Description:
 */

import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import type { TopBarSearchItem } from '@/components/TopBar';
import {
  InterfaceBreadOptions,
  InterfaceColumns,
  InterfaceSearchOption,
} from '@/constants/permission/interface';
import { deleteInterface } from '@/services/permission';
import { fetchData } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { PlusOutlined } from '@ant-design/icons';
import { message, Modal } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';
import AddInterfaceDrawer from '../components/AddInterfaceDrawer';
const Page: React.FC = () => {
  const { dataSource, reload, loading } = useModel('useInterfaceList');

  const columns = useMemo(() => {
    const handleDeleteInterface = (params: any) => {
      Modal.confirm({
        title: 'Delete Interface',
        content: `Are you sure to delete ${params.itf_name}?`,
        onOk: () => {
          fetchData({
            request: deleteInterface,
            params: params,
            onSuccess: () => {
              message.success('Delete Success');
              reload();
            },
          });
        },
      });
    };
    const handleEditInterface = (params: PermissionAPI.InterfaceItem) => {
      setCurrentInterface(params);
      setIsEdit(true);
      setVisible(true);
    };

    return [
      ...InterfaceColumns,
      genOperateColumn<PermissionAPI.InterfaceItem>({
        width: 160,
        btnOptions: [
          {
            label: 'Edit',
            onClick: handleEditInterface,
            icon: <RixEngineFont type="edit" />,
          },
          {
            label: 'Delete',
            onClick: handleDeleteInterface,
            icon: <RixEngineFont type="rix-trash" style={{ color: 'red' }} />,
            isDelete: true,
          },
        ],
      }),
    ];
  }, []);
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [Interface, setCurrentInterface] = useState<
    PermissionAPI.InterfaceItem | undefined
  >(undefined);
  const [searchOptions, setSearchOptions] = useState(InterfaceSearchOption);

  useEffect(() => {
    reload();
    handleSearchOptions();
  }, []);

  useEffect(() => {
    handleSearchOptions();
  }, [dataSource]);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (dataSource && dataSource.length) {
      const itfOptions: any[] = dataSource.map(
        (item: PermissionAPI.InterfaceItem) => {
          return {
            label: `${item.itf_name}`,
            value: item.id,
          };
        },
      );

      // const idOptions: any[] = dataSource.map(
      //   (item: PermissionAPI.InterfaceItem) => {
      //     return {
      //       label: `${item.id}`,
      //       value: item.id,
      //     };
      //   },
      // );

      const itfIndex = options.findIndex((item) => item.key === 'id');
      // const idIndex = options.findIndex((item) => item.key === 'id');

      // idIndex !== -1 && (options[idIndex].options = idOptions);
      itfIndex !== -1 && (options[itfIndex].options = itfOptions);
    }

    setSearchOptions(options);
  };

  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurrentInterface(undefined);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={InterfaceBreadOptions}>
      <FrontTable<PermissionAPI.InterfaceItem>
        pageTitle="Interface"
        searchOptions={searchOptions}
        loading={loading && dataSource && !dataSource.length}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Create Interface',
            type: 'primary',
            size: 'small',
            onClick: handleClickCreate,
            icon: <PlusOutlined />,
          },
        ]}
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        labelWidth={110}
        emptyRender={
          dataSource && dataSource.length ? normalEmptyRender : undefined
        }
        isFold={true}
      />
      <AddInterfaceDrawer
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadInterface={reload}
        Interface={Interface}
      />
    </PageContainer>
  );
};

export default Page;
