import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  AccountLinkBreadOptions,
  AccountLinkColumnOptions,
} from '@/constants/manage/account-link';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { PlusOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import AccountLinkDrawer from './account-link-drawer';
import { getSearchOptions } from './helper';

const Page: React.FC = () => {
  const {
    accountLinkList = [],
    reload,
    loading,
  } = useModel('useAccountLinkList');

  const { dataSource, searchOptions, existSpecialUser } = useMemo(() => {
    const userNameOptions = accountLinkList.map(
      ({ account_name, user_id }: UserAPI.AccountLinkColumn) => ({
        label: `${account_name} (${user_id})`,
        value: account_name,
      }),
    );

    return {
      dataSource: accountLinkList,
      searchOptions: getSearchOptions({ userNameOptions }),
      existSpecialUser: accountLinkList.map(
        ({ user_id }: UserAPI.AccountLinkColumn) => user_id,
      ),
    };
  }, [accountLinkList]);

  const [openDrawer, setOpenDrawer] = useState(false);
  const [currentRow, setCurrentRow] =
    useState<UserAPI.AccountLinkColumn | null>(null);

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
  };

  const handleAddAccountLink = () => {
    setCurrentRow(null);
    setOpenDrawer(true);
  };

  const columns = useMemo(() => {
    const handleEditAccountLink = (record: UserAPI.AccountLinkColumn) => {
      setCurrentRow(record);
      setOpenDrawer(true);
    };

    return [
      ...AccountLinkColumnOptions,
      genOperateColumn<UserAPI.AccountLinkColumn>({
        width: 80,
        access: 'ManageEditAccountLink',
        btnOptions: [
          {
            label: 'Edit',
            onClick: handleEditAccountLink,
            icon: <RixEngineFont type="edit" />,
          },
        ],
      }),
    ];
  }, []);

  useEffect(() => {
    reload();
  }, []);

  return (
    <PageContainer options={AccountLinkBreadOptions}>
      <FrontTable<UserAPI.AccountLinkColumn>
        pageTitle="Account Link"
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'user_id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Link',
            type: 'primary',
            icon: <PlusOutlined />,
            size: 'small',
            onClick: handleAddAccountLink,
            access: 'ManageAddAccountLinkCode',
          },
        ]}
        labelWidth={80}
        scroll={{ x: 1000, y: 'auto' }}
        isFold={true}
      />
      <AccountLinkDrawer
        visible={openDrawer}
        currentRow={currentRow}
        existSpecialUser={existSpecialUser}
        handleClose={handleCloseDrawer}
        refresh={reload}
      />
    </PageContainer>
  );
};

export default Page;
