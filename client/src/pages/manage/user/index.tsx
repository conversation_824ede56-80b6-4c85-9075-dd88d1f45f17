import { useModel } from '@umijs/max';
import { Form, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';

import NormalModal from '@/components/Modal/NormalModal';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  TntUserColumnOptions,
  UserBreadOptions,
  UserSearchOption,
} from '@/constants/manage/user';
import { genOperateColumn } from '@/utils/genOperateColumn';
import AddUser from './components/AddUser';

import type { TopBarSearchItem } from '@/components/TopBar';

import { deleteUser } from '@/services/api';
import { fetchData } from '@/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRow, setCurrentRow] = useState<TenantAPI.UserListItem>();
  const [dataSource, setDataSource] = useState<TenantAPI.UserListItem[]>([]);
  const [tenantOptopns, setTenantOptopns] = useState<any[]>([]);
  const [searchOptions, setSearchOptions] = useState<any[]>(UserSearchOption);
  const { dataSource: userList, reload, loading } = useModel('useUserList');
  const { tenantList, reload: reloadTenantList } = useModel('useTenantList');
  const attentionModel = (params: any) => {
    NormalModal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Are you sure? This user will be permanently deleted and no longer
            accessible through API.
          </div>
          <div>Do you want to delete this user?</div>
        </>
      ),
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: () => {
        const deleteSuccess = () => {
          message.success('Delete success');
          reload();
        };
        fetchData({
          request: deleteUser,
          params: params,
          onSuccess: deleteSuccess,
        });
      },
      okButtonProps: {
        danger: true,
      },
    });
  };

  const column = useMemo(() => {
    const handleEditUser = (params: TenantAPI.UserListItem) => {
      setVisible(true);
      setIsEdit(true);
      setCurrentRow(params);
    };
    const handleUserDelete = (params: any) => {
      attentionModel(params);
    };

    const operateColumn = genOperateColumn<TenantAPI.UserListItem>({
      width: 170,
      access: 'ManageEditUser',
      btnOptions: [
        {
          label: 'Edit',
          onClick: handleEditUser,
          icon: <RixEngineFont type="edit" />,
        },
        {
          label: 'Delete',
          onClick: handleUserDelete,
          icon: (
            <RixEngineFont type="rix-trash" className={styles['delete-btn']} />
          ),
          isDelete: true,
          hide: process.env.UMI_ENV === 'prod',
        },
      ],
    });
    return [...TntUserColumnOptions, operateColumn];
  }, []);
  useEffect(() => {
    if (initialState?.currentUser) {
      form.setFieldsValue(initialState.currentUser);
    }
  }, [initialState?.currentUser]);

  useEffect(() => {
    reload();
    reloadTenantList();
  }, []);
  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (Array.isArray(userList)) {
      setDataSource(userList);
      const tmpMap = new Set();
      const tmp = userList
        .filter((item) => {
          if (!tmpMap.has(item.account_name)) {
            tmpMap.add(item.account_name);
            return true;
          }
          return false;
        })
        .map((item) => {
          return {
            label: item.account_name,
            value: item.account_name,
          };
        });
      const index = options.findIndex((item) => item.key === 'account_name');
      if (index !== -1) {
        options[index].options = tmp;
      }
    }
    if (Array.isArray(tenantList)) {
      const tmpTenantOptions = tenantList.map((item) => ({
        label: `${item.tnt_name}(${item.tnt_id})`,
        value: item.tnt_id,
      }));
      setTenantOptopns(tmpTenantOptions);

      const sIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = tmpTenantOptions;
      }
    }
    setSearchOptions(options);
  }, [userList, tenantList]);

  const handleAddUser = () => {
    setIsEdit(false);
    setVisible(true);
  };

  return (
    <PageContainer options={UserBreadOptions}>
      <FrontTable<TenantAPI.UserListItem>
        pageTitle="User Management"
        searchOptions={searchOptions}
        loading={loading}
        columns={column}
        dataSource={dataSource}
        rowKey={'user_id'}
        request={reload}
        btnOptions={[
          {
            label: '+ Add Super Administrator',
            type: 'primary',
            size: 'small',
            onClick: handleAddUser,
            access: 'ManageAddUserCode',
          },
        ]}
        labelWidth={80}
        scroll={{ x: 1000, y: 'auto' }}
        isFold={true}
      />
      <AddUser
        visible={visible}
        isEdit={isEdit}
        setVisible={setVisible}
        rowUserInfo={currentRow}
        reloadUserList={reload}
        tenantOptions={tenantOptopns}
      />
    </PageContainer>
  );
};

export default Page;
