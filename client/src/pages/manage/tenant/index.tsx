import NormalModal from '@/components/Modal/NormalModal';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import type { TopBarSearchItem } from '@/components/TopBar';
import {
  TenantSearchOption,
  TenantBreadOptions,
  TenantColumnOptions,
  TenantDefaultFilterValues,
} from '@/constants/manage/tenant';
import { deleteTenant } from '@/services/api';
import { fetchData } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import AddTenant from './components/AddTenant';
import styles from './index.less';

const Page: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRow, setCurrentRow] = useState<TenantAPI.TenantListItem>();

  const [searchOptions, setSearchOptions] = useState<any[]>(TenantSearchOption);
  const { tenantList, reload, loading } = useModel('useTenantList');
  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );

    if (Array.isArray(tenantList)) {
      const tmpTenantOptions = tenantList.map((item) => ({
        label: `${item.tnt_name}(${item.tnt_id})`,
        value: item.tnt_id,
      }));

      const sIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = tmpTenantOptions;
      }
    }
    setSearchOptions(options);
  }, [tenantList]);
  const attentionModel = (params: any) => {
    NormalModal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Are you sure? This user will be permanently deleted and no longer
            accessible through API.
          </div>
          <div>Do you want to delete this tenant?</div>
        </>
      ),
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: () => {
        const deleteSuccess = () => {
          message.success('Delete success');
          reload();
        };
        fetchData({
          request: deleteTenant,
          params: params,
          onSuccess: deleteSuccess,
        });
      },
      okButtonProps: {
        danger: true,
      },
    });
  };

  const column = useMemo(() => {
    const handleEditTenant = (row: Readonly<TenantAPI.TenantListItem>) => {
      setVisible(true);
      setIsEdit(true);
      setCurrentRow(row);
    };

    const handleTenantDelete = (row: Readonly<TenantAPI.TenantListItem>) => {
      attentionModel({ tnt_id: row.tnt_id, tnt_name: row.tnt_name });
    };

    return [
      ...TenantColumnOptions,
      genOperateColumn<TenantAPI.TenantListItem>({
        width: 170,
        access: 'TntOperateCol',
        btnOptions: [
          {
            label: 'Edit',
            onClick: handleEditTenant,
            icon: <RixEngineFont type="edit" />,
          },
          {
            label: 'Delete',
            onClick: handleTenantDelete,
            icon: (
              <RixEngineFont
                type="rix-trash"
                className={styles['delete-btn']}
              />
            ),
            isDelete: true,
            hide: process.env.UMI_ENV === 'prod',
          },
        ],
      }),
    ];
  }, []);

  useEffect(() => {
    reload();
  }, []);

  const handleTenant = () => {
    setVisible(true);
    setIsEdit(false);
  };

  return (
    <PageContainer options={TenantBreadOptions}>
      <FrontTable<TenantAPI.TenantListItem>
        pageTitle="Tenant Management"
        searchOptions={searchOptions}
        loading={loading}
        columns={column}
        initialValues={TenantDefaultFilterValues}
        dataSource={tenantList ?? []}
        isFold
        rowKey={'tnt_id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Tenant',
            type: 'primary',
            size: 'small',
            onClick: handleTenant,
            icon: <RixEngineFont type="add" />,
            access: 'AddTenantCode',
          },
        ]}
        labelWidth={100}
        scroll={{ x: 1000, y: 'auto' }}
      />
      <AddTenant
        visible={visible}
        isEdit={isEdit}
        setVisible={setVisible}
        rowTenantInfo={currentRow}
        reloadTenantList={reload}
      />
    </PageContainer>
  );
};

export default Page;
