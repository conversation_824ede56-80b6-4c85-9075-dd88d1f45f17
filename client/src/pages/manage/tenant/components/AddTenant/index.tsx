import React, { useEffect } from 'react';

import { Form, Radio, message, Input } from 'antd';

import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';

import NormalDrawer from '@/components/Drawer/NormalDrawer';

import { StatusOptions } from '@/constants';

import { addTenant, editTenant } from '@/services/api';
import { fetchData } from '@/utils';

type AddTenantProps = {
  isEdit: boolean;
  visible: boolean;
  setVisible: (params: boolean) => void;
  rowTenantInfo?: TenantAPI.TenantListItem;
  reloadTenantList: () => void;
};
const DefaultValue = {
  pl_status: 2,
  hm_status: 2,
  pv_domain: '',
};
const AddTenant: React.FC<AddTenantProps> = ({
  isEdit,
  visible,
  setVisible,
  rowTenantInfo,
  reloadTenantList,
}) => {
  const [form] = Form.useForm();
  useEffect(() => {
    if (visible) {
      if (isEdit) {
        form.setFieldsValue({
          ...rowTenantInfo,
        });
      } else {
        form.setFieldsValue(DefaultValue);
      }
    } else {
      form.setFieldsValue(DefaultValue);
    }
  }, [isEdit, visible, rowTenantInfo]);

  const handleConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    message.success('Success');
    reloadTenantList();
    setVisible(false);
    form.resetFields();
  };
  const handleFinish = (values: any) => {
    const params = {
      ...values,
      host_prefix: values.host_prefix?.trim(),
      tnt_name: values.tnt_name?.trim(),
      tnt_id: rowTenantInfo?.tnt_id,
      ori_data: isEdit ? rowTenantInfo : {},
    };
    fetchData({
      request: isEdit ? editTenant : addTenant,
      params: params,
      onSuccess,
    });
  };

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };
  const validateDomain = (rule: any, value: any) => {
    const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
    if (!value) {
      return Promise.resolve();
    } else if (!reg.test(value)) {
      return Promise.reject(
        new Error('Please enter the correct domain format'),
      );
    }
    return Promise.resolve();
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Tenant` : `Add Tenant`}
      loading={false}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleCancel}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
      >
        <Form.Item
          label="Tenant Name"
          name="tnt_name"
          rules={[{ required: true, message: 'Please Input Tenant Name!' }]}
        >
          <NormalInput placeholder="Please Input Tenant Name" />
        </Form.Item>
        <Form.Item
          label="Host Prefix"
          name="host_prefix"
          rules={[{ required: true, message: 'Please Input Host Prefix!' }]}
        >
          <NormalInput placeholder="Please Input Host Prefix!" />
        </Form.Item>

        {/* <Form.Item
          label="Brand"
          name="brand"
          rules={[{ required: true, message: 'Please Input Brand!' }]}
        >
          <NormalInput placeholder="Please Input Company"></NormalInput>
        </Form.Item> */}
        {/* 防止表单填充 */}
        <Input
          name="hiddenField"
          style={{
            height: '0px',
            width: '0px',
            overflow: 'hidden',
            padding: '0px',
            border: 'none',
            position: 'absolute',
          }}
          maxLength={11}
        />
        <Form.Item
          label="E-mail"
          name="email"
          rules={[
            {
              required: true,
              message: 'Please Input E-mail!',
            },
            {
              type: 'email',
              message: 'Please enter the correct E-mail!',
            },
          ]}
        >
          <NormalInput
            placeholder="Please Input E-mail"
            autoComplete="off"
          ></NormalInput>
        </Form.Item>
        <Form.Item
          label="Company"
          name="company"
          rules={[{ required: true, message: 'Please Input Company!' }]}
        >
          <NormalInput placeholder="Please Input Company"></NormalInput>
        </Form.Item>
        <Form.Item
          label="Contact"
          name="contact"
          rules={[{ required: true, message: 'Please Input Contact!' }]}
        >
          <NormalInput placeholder="Please Input Contact"></NormalInput>
        </Form.Item>
        <Form.Item
          label="Phone"
          name="phone"
          rules={[{ required: true, message: 'Please Input Phone!' }]}
        >
          <NormalInput placeholder="Please Input Phone"></NormalInput>
        </Form.Item>
        <Form.Item
          label="Private Domain"
          name="pv_domain"
          rules={[{ validator: validateDomain }]}
        >
          <NormalInput placeholder="Please Input Private Domain!" />
        </Form.Item>
        {isEdit && (
          <>
            <Form.Item label="CS_Domain" name="cs_domain">
              <NormalInput placeholder="Please Input CS Domain!" disabled />
            </Form.Item>
            <Form.Item
              label="Token"
              name="token"
              rules={[{ required: true, message: 'Please Input Token!' }]}
            >
              <NormalInput disabled={isEdit} />
            </Form.Item>
            <Form.Item
              label="Status"
              name="status"
              rules={[{ required: true }]}
            >
              <NormalRadio>
                {StatusOptions.map((item, index) => (
                  <Radio key={index} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            </Form.Item>
          </>
        )}
        <Form.Item
          label="Pixalate Status"
          name="pl_status"
          rules={[{ required: true }]}
        >
          <NormalRadio>
            {StatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          label="Human Status"
          name="hm_status"
          rules={[{ required: true }]}
        >
          <NormalRadio>
            {StatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default AddTenant;
