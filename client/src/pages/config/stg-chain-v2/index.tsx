/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-09-18 18:10:24
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-06 19:41:12
 * @Description:
 */
/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 15:27:36
 * @LastEditors: chen<PERSON>dan <EMAIL>
 * @LastEditTime: 2023-08-09 10:38:56
 * @Description:
 */
import NormalModal from '@/components/Modal/NormalModal';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  StgBreadOptions,
  StgColumnOptions,
  StgSearchOption,
} from '@/constants/config/stg-v2';
import { downloadCsv } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import { Checkbox, Col, Form, Row } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import AddStgChainV2 from '../components/AddStgChainV2';

const Page: React.FC = () => {
  const { dataSource, loading, reload } = useModel('useStgChainListV2');
  const {
    tenantList,
    reload: reloadTnt,
    loading: tntLoading,
  } = useModel('useTenantList');

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.StgItem>();
  const [searchOptions, setSearchOptions] = useState(StgSearchOption);
  const [defaultParams] = useState({ status: 1 });
  const [defaultExportColumns] = useState([
    'publisher_id',
    'type',
    'developer_website_domain',
  ]);
  const [form] = Form.useForm();

  const handleEdit = useCallback((row: ConfigAPI.StgItem) => {
    setEditItem(row);
    setIsEdit(true);
    setVisible(true);
  }, []);

  const columns = useMemo(() => {
    return [
      ...StgColumnOptions,
      genOperateColumn<ConfigAPI.StgItem>({
        width: 120,
        access: 'EditSupplyChain',
        btnOptions: [
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            onClick: handleEdit,
          },
        ],
      }),
    ];
  }, [handleEdit]);

  useEffect(() => {
    reload();
    reloadTnt();
  }, []);

  useEffect(() => {
    const options = StgSearchOption.map((v) => ({ ...v }));
    let tenantOptions = Array.isArray(tenantList)
      ? tenantList.map((v) => ({
          label: `${v.tnt_name}(${v.tnt_id})`,
          value: v.tnt_id,
        }))
      : [];
    tenantOptions.unshift({ label: 'All Tenants', value: 0 });
    const tIndex = options.findIndex((v) => v.key === 'tnt_id');
    const pIndex = options.findIndex((v) => v.key === 'publisher_id');
    const dIndex = options.findIndex(
      (v) => v.key === 'developer_website_domain',
    );
    if (tIndex !== -1) {
      options[tIndex].options = tenantOptions;
    }
    if (pIndex !== -1) {
      options[pIndex].options =
        dataSource?.map((v, index) => {
          return { label: v.publisher_id, value: v.publisher_id, key: index };
        }) || [];
    }
    if (dIndex !== -1) {
      options[dIndex].options =
        dataSource?.map((v, index) => {
          return {
            label: v.developer_website_domain,
            value: v.developer_website_domain,
            key: index,
          };
        }) || [];
    }

    setSearchOptions(options);
  }, [tenantList, dataSource]);

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };

  // tableData
  const tableDataRef = useRef<ConfigAPI.StgItem[]>([]);
  const handleFinish = (values: any) => {
    const { export_columns } = values;
    const fileName = `report_${new Date().getTime()}`;
    const fields: { label: string; value: string }[] = [];
    columns.forEach((item) => {
      if (export_columns.includes(item.dataIndex as string)) {
        fields.push({
          label: item.title as string,
          value: item.dataIndex as string,
        });
      }
    });
    downloadCsv(fileName, tableDataRef.current, { fields });
    form.resetFields();
    tableDataRef.current = [];
  };
  const handleExport = (tableData: ConfigAPI.StgItem[]) => {
    const checkBoxOptions = columns
      .map((v) => ({
        label: v.title as string,
        value: v.dataIndex as string,
      }))
      .filter((v) => v.value !== 'operate');

    NormalModal.confirm({
      title: 'Export',
      content: (
        <Form onFinish={handleFinish} form={form}>
          <Form.Item
            noStyle
            name="export_columns"
            initialValue={defaultExportColumns}
          >
            <Checkbox.Group>
              <Row>
                {checkBoxOptions.map((v, index) => (
                  <Col key={index} span={12}>
                    <Checkbox value={v.value}>{v.label}</Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      ),
      onOk: () => {
        tableDataRef.current = tableData;
        form.submit();
      },
      onCancel: () => {
        tableDataRef.current = [];
        form.resetFields();
      },
    });
  };
  return (
    <PageContainer options={StgBreadOptions}>
      <FrontTable<ConfigAPI.StgItem>
        pageTitle="Supply Chain"
        searchOptions={searchOptions}
        loading={loading || tntLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Supply Chain',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            access: 'AddSupplyChain',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        initialValues={defaultParams}
        isExport
        handleExport={handleExport}
      />
      <AddStgChainV2
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        tenantList={tenantList || []}
      />
    </PageContainer>
  );
};

export default Page;
