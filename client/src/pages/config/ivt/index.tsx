/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 15:27:36
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 19:07:49
 * @Description:
 */
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  IvtConfigBreadOptions,
  IvtConfigColumnOptions,
  IvtConfigSearchOption,
} from '@/constants/config/ivt';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import AddIvtConfig from '../components/AddIvtConfig';

const Page: React.FC = () => {
  const { dataSource, loading, reload } = useModel('useIvtConfigListList');
  const { allDemandList: demandList, reload: reloadDemand } =
    useModel('useAllDemandList');
  const { allSupplyList: supplyList, reload: reloadSupply } =
    useModel('useAllSupplyList');
  const {
    tenantList,
    reload: reloadTnt,
    loading: tntLoading,
  } = useModel('useTenantList');

  const demandOptions = useMemo(() => {
    return (
      demandList?.map((v) => ({
        label: `${v.buyer_name}(${v.buyer_id})`,
        value: v.buyer_id,
        tnt_id: v.tnt_id,
      })) || []
    );
  }, [demandList]);
  const supplyOptions = useMemo(() => {
    return (
      supplyList?.map((v) => ({
        label: `${v.seller_name}(${v.seller_id})`,
        value: v.seller_id,
        tnt_id: v.tnt_id,
      })) || []
    );
  }, [supplyList]);
  const tenantOptions = useMemo(() => {
    return (
      tenantList?.map((v) => ({
        label: `${v.tnt_name}(${v.tnt_id})`,
        value: v.tnt_id,
        tnt_id: v.tnt_id,
      })) || []
    );
  }, [tenantList]);

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.IvtConfigItem>();
  const [searchOptions, setSearchOptions] = useState(IvtConfigSearchOption);
  const [defaultParams] = useState({ status: 1 });

  const columns = useMemo(() => {
    const handleEdit = (row: ConfigAPI.IvtConfigItem) => {
      setEditItem(row);
      setIsEdit(true);
      setVisible(true);
    };

    const operateColumn = genOperateColumn<ConfigAPI.IvtConfigItem>({
      width: 120,
      access: 'EditIVTCode',
      btnOptions: [
        {
          label: 'Edit',
          icon: <RixEngineFont type="edit" />,
          onClick: handleEdit,
        },
      ],
    });

    return [...IvtConfigColumnOptions, operateColumn];
  }, []);

  useEffect(() => {
    reload();
    reloadTnt();
    reloadDemand();
    reloadSupply();
  }, []);

  useEffect(() => {
    const options = IvtConfigSearchOption.map((v) => ({ ...v }));

    const optionsMap: Record<string, any[]> = {
      tnt_id: tenantOptions,
      seller_id: supplyOptions,
      buyer_id: demandOptions,
    };

    options.forEach((option) => {
      if (optionsMap[option.key]) {
        option.options = optionsMap[option.key];
      }
    });

    setSearchOptions(options);
  }, [tenantOptions, demandOptions, supplyOptions]);

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };
  const handleSearchValueChange = (changeValue: any, allChangeValue: any) => {
    console.log('changeValue', changeValue);
    if (changeValue.tnt_id) {
      const newSearchOptions = [...searchOptions];
      const tntId = changeValue.tnt_id;
      const hasChanges = [
        { key: 'buyer_id', options: demandOptions },
        { key: 'seller_id', options: supplyOptions },
      ].reduce((changed, { key, options }) => {
        if (!options.length) return changed;

        const filteredOptions = options.filter(
          (v) => !tntId.length || tntId.includes(v.tnt_id),
        );

        for (let i = 0; i < newSearchOptions.length; i++) {
          if (newSearchOptions[i].key === key) {
            newSearchOptions[i].options = filteredOptions;
            return true;
          }
        }
        return changed;
      }, false);

      if (hasChanges) {
        setSearchOptions(newSearchOptions);
      }
    }
  };
  return (
    <PageContainer options={IvtConfigBreadOptions}>
      <FrontTable<ConfigAPI.IvtConfigItem>
        pageTitle="IVT Config"
        searchOptions={searchOptions}
        loading={loading || tntLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add IVT Config',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            access: 'AddIVTCode',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        initialValues={defaultParams}
        handleSearchValueChange={handleSearchValueChange}
      />
      <AddIvtConfig
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        tenantOptions={tenantOptions}
        demandOptions={demandOptions}
        supplyOptions={supplyOptions}
      />
    </PageContainer>
  );
};

export default Page;
