/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-09-18 18:10:24
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-06 19:41:12
 * @Description:
 */

import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  BundleStgSearchOption,
  DomainStgSearchOption,
  StgBreadOptions,
  StgColumnOptions,
  StgTab,
  StgTabOptions,
} from '@/constants/config/stg';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import AddStgChain from '../components/AddStgChain';
import AddStgChainV2 from '../components/AddStgChainV2';

import NormalModal from '@/components/Modal/NormalModal';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { downloadCsv } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { Checkbox, Col, Form, FormInstance, Row } from 'antd';

const Page: React.FC = () => {
  const {
    dataSource: bundleData,
    loading: loadingBundle,
    reload: reloadBundle,
  } = useModel('useStgChainList');
  const {
    dataSource: domainData,
    loading: loadingDomain,
    reload: reloadDomain,
  } = useModel('useStgChainListV2');
  const {
    tenantList,
    reload: reloadTnt,
    loading: tntLoading,
  } = useModel('useTenantList');

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.StgItem>();
  const [searchOptions, setSearchOptions] = useState(BundleStgSearchOption);
  const [defaultParams] = useState({ status: 1 });
  const [defaultExportColumns] = useState(['publisher_id', 'type', 'bundle']);
  const [currentTab, setCurrentTab] = useState(StgTab.bundle);
  const [form] = Form.useForm();
  const searchFormRef = useRef<FormInstance<any>>(null);

  const columns = useMemo(() => {
    const handleEdit = (row: ConfigAPI.StgItem) => {
      setEditItem(row);
      setIsEdit(true);
      setVisible(true);
    };

    const operateColumn = genOperateColumn<ConfigAPI.StgItem>({
      width: 120,
      btnOptions: [
        {
          label: 'Edit',
          icon: <RixEngineFont type="edit" />,
          onClick: handleEdit,
        },
      ],
    });

    // 处理 domain tab 的特殊情况
    // 创建 StgColumnOptions 的本地副本以避免修改原始导入的常量
    const localColumnOptions = StgColumnOptions.map(col => ({ ...col }));
    const bIndex = localColumnOptions.findIndex((v) => v.dataIndex === 'bundle');
    if (bIndex !== -1 && currentTab === StgTab.domain) {
      localColumnOptions[bIndex].dataIndex = 'developer_website_domain';
      localColumnOptions[bIndex].title = 'Domain';
      localColumnOptions[bIndex].render = (_, params) => (
        <HoverToolTip title={`${params.developer_website_domain}`}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }

    return [...localColumnOptions, operateColumn];
  }, [currentTab]);

  useEffect(() => {
    reloadTnt();
  }, []);

  useEffect(() => {
    let options =
      currentTab === StgTab.bundle
        ? BundleStgSearchOption
        : DomainStgSearchOption;
    //
    options = options.map((v) => ({ ...v }));
    let dataSource = currentTab === StgTab.bundle ? bundleData : domainData;
    let tenantOptions = Array.isArray(tenantList)
      ? tenantList.map((v) => ({
          label: `${v.tnt_name}(${v.tnt_id})`,
          value: v.tnt_id,
        }))
      : [];
    tenantOptions.unshift({ label: 'All Tenants', value: 0 });
    const tIndex = options.findIndex((v) => v.key === 'tnt_id');
    const pIndex = options.findIndex((v) => v.key === 'publisher_id');
    const dIndex = options.findIndex((v) => v.key === 'bundle');
    const dmIndex = options.findIndex(
      (v) => v.key === 'developer_website_domain',
    );
    if (tIndex !== -1) {
      options[tIndex].options = tenantOptions;
    }
    if (pIndex !== -1) {
      const arr = [
        ...new Set(
          dataSource?.map((v) => v.publisher_id).filter((v) => v) || [],
        ),
      ];
      options[pIndex].options = arr.map((v, index) => ({
        label: v,
        value: v,
        key: index,
      }));
    }
    if (dIndex !== -1) {
      const arr = [
        ...new Set(
          dataSource?.map((v) => v.bundle).filter((v) => v && v.trim()) || [],
        ),
      ];
      options[dIndex].options = arr.map((v, index) => ({
        label: v,
        value: v,
        key: index,
      }));
    }
    if (dmIndex !== -1) {
      const arr = [
        ...new Set(
          dataSource
            ?.map((v) => v.developer_website_domain)
            .filter((v) => v && v.trim()) || [],
        ),
      ];
      options[dmIndex].options = arr.map((v, index) => ({
        label: v,
        value: v,
        key: index,
      }));
    }

    setSearchOptions(options);
  }, [tenantList, bundleData, domainData, currentTab]);

  useEffect(() => {
    if (currentTab) {
      currentTab === StgTab.bundle ? reloadBundle() : reloadDomain();
    }
  }, [currentTab]);

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    currentTab === StgTab.bundle ? reloadBundle() : reloadDomain();
  };

  // tableData
  const tableDataRef = useRef<ConfigAPI.StgItem[]>([]);
  const handleFinish = (values: any) => {
    const { export_columns } = values;
    const fileName = `report_${new Date().getTime()}`;
    const fields: { label: string; value: string }[] = [];
    columns.forEach((item) => {
      if (export_columns.includes(item.dataIndex as string)) {
        fields.push({
          label: item.title as string,
          value: item.dataIndex as string,
        });
      }
    });
    downloadCsv(fileName, tableDataRef.current, { fields });
    form.resetFields();
    tableDataRef.current = [];
  };
  const handleExport = (tableData: ConfigAPI.StgItem[]) => {
    const checkBoxOptions = columns
      .map((v) => ({
        label: v.title as string,
        value: v.dataIndex as string,
      }))
      .filter((v) => v.value !== 'operate');

    NormalModal.confirm({
      title: 'Export',
      content: (
        <Form onFinish={handleFinish} form={form}>
          <Form.Item
            noStyle
            name="export_columns"
            initialValue={defaultExportColumns}
          >
            <Checkbox.Group>
              <Row>
                {checkBoxOptions.map((v, index) => (
                  <Col key={index} span={12}>
                    <Checkbox value={v.value}>{v.label}</Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      ),
      onOk: () => {
        tableDataRef.current = tableData;
        form.submit();
      },
      onCancel: () => {
        form.resetFields();
        tableDataRef.current = [];
      },
    });
  };
  const handleTabChange = (tab: string | number) => {
    setCurrentTab(tab as string);
  };

  return (
    <PageContainer options={StgBreadOptions}>
      <FrontTable<ConfigAPI.StgItem>
        searchFormRef={searchFormRef}
        searchOptions={searchOptions}
        loading={loadingBundle || loadingDomain || tntLoading}
        columns={columns}
        dataSource={currentTab === StgTab.bundle ? bundleData : domainData}
        rowKey={'id'}
        request={currentTab === StgTab.bundle ? reloadBundle : reloadDomain}
        btnOptions={[
          {
            label: 'Add Supply Chain',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        initialValues={defaultParams}
        isExport
        handleExport={handleExport}
        tabOptions={StgTabOptions}
        onTabChange={handleTabChange}
        defaultTab={currentTab}
      />
      {currentTab === StgTab.bundle ? (
        <AddStgChain
          visible={visible}
          isEdit={isEdit}
          item={editItem}
          onClose={handleClose}
          onSave={handleSave}
          tenantList={tenantList || []}
        />
      ) : (
        <AddStgChainV2
          visible={visible}
          isEdit={isEdit}
          item={editItem}
          onClose={handleClose}
          onSave={handleSave}
          tenantList={tenantList || []}
        />
      )}
    </PageContainer>
  );
};

export default Page;
