/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-04-03 16:38:57
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-08 14:14:45
 * @Description:
 */

import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  PixlBreadOptions,
  PixlColumnOptions,
  PixlSearchOption,
} from '@/constants/config/pixalate-prebid';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import AddPixlPrebid from '../components/AddPixlPrebid';

const Page: React.FC = () => {
  const { dataSource, loading, reload } = useModel('usePixalatePrebidList');
  const { allSupplyList: supplyList, reload: reloadSupply } =
    useModel('useAllSupplyList');
  const { allDemandList: demandList, reload: reloadDemand } =
    useModel('useAllDemandList');
  const { tenantList, reload: reloadTnt } = useModel('useTenantList');

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.PixlItem>();
  const [searchOptions, setSearchOptions] = useState(PixlSearchOption);

  const handleSearchOptions = (tnt_id?: number[]) => {
    const options = [...PixlSearchOption];
    if (
      Array.isArray(tenantList) &&
      Array.isArray(supplyList) &&
      Array.isArray(demandList)
    ) {
      const tenantOptions = tenantList.map((v) => ({
        label: `${v.tnt_name}(${v.tnt_id})`,
        value: v.tnt_id,
      }));

      const supplyOptions = supplyList
        .filter((s) => tnt_id?.includes(s.tnt_id) || !tnt_id)
        .map((v) => ({
          label: `${v.tnt_id}-${v.seller_name}(${v.seller_id})`,
          value: v.seller_id,
        }));

      const demandOptions = demandList
        .filter((b) => tnt_id?.includes(b.tnt_id) || !tnt_id)
        .map((v) => ({
          label: `${v.tnt_id}-${v.buyer_name}(${v.buyer_id})`,
          value: v.buyer_id,
        }));
      const tIndex = options.findIndex((v) => v.key === 'tnt_id');
      const sIndex = options.findIndex((v) => v.key === 'seller_id');
      const bIndex = options.findIndex((v) => v.key === 'buyer_id');
      if (tIndex !== -1) {
        options[tIndex].options = tenantOptions;
      }
      if (sIndex !== -1) {
        options[sIndex].options = supplyOptions;
      }
      if (bIndex !== -1) {
        options[bIndex].options = demandOptions;
      }

      setSearchOptions(options);
    }
  };

  useEffect(() => {
    reload();
    reloadTnt();
    reloadSupply();
    reloadDemand();
    handleSearchOptions();
  }, []);

  useEffect(() => {
    handleSearchOptions();
  }, [tenantList, supplyList, demandList]);

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };
  const handleSearchValueChange = (value: any) => {
    if (value.tnt_id) {
      handleSearchOptions(value.tnt_id);
    }
  };

  const columns = useMemo(() => {
    const handleEdit = (row: ConfigAPI.PixlItem) => {
      setEditItem(row);
      setIsEdit(true);
      setVisible(true);
    };

    const operateColumn = genOperateColumn<ConfigAPI.PixlItem>({
      access: 'EditPixalatePrebid',
      width: 120,
      btnOptions: [
        {
          label: 'Edit',
          icon: <RixEngineFont type="edit" />,
          onClick: handleEdit,
        },
      ],
    });

    return [...PixlColumnOptions, operateColumn];
  }, []);

  return (
    <PageContainer options={PixlBreadOptions}>
      <FrontTable<ConfigAPI.PixlItem>
        pageTitle="Pixalate Prebid"
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Pixalate Prebid',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            access: 'AddPixlPrebid',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        initialValues={{ status: 1 }}
        handleSearchValueChange={handleSearchValueChange}
      />
      <AddPixlPrebid
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        tenantList={tenantList || []}
        supplyList={supplyList || []}
        demandList={demandList || []}
      />
    </PageContainer>
  );
};

export default Page;
