/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 14:13:23
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 19:12:17
 * @Description:
 */

import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import { KwaColumnOptions, KwaSearchOption } from '@/constants/config/kwai';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import { CommonPlacementProps } from '.';
import AddKwaModel from '../components/AddKwaModel';

const KwaiPlacementPage: React.FC<CommonPlacementProps> = ({
  tabOptions,
  onTabChange,
  tntLoading,
  tenantList,
  allDemandList,
}) => {
  const { dataSource, loading, reload } = useModel('useKwaList');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.KwaiPlacementItem>();
  const [searchOptions, setSearchOptions] = useState(KwaSearchOption);
  const [defaultParams] = useState({ status: 1 });

  const columns = useMemo(() => {
    const handleEdit = (row: ConfigAPI.KwaiPlacementItem) => {
      setEditItem(row);
      setIsEdit(true);
      setVisible(true);
    };

    const operateColumn = genOperateColumn<ConfigAPI.KwaiPlacementItem>({
      width: 120,
      access: 'EditKwaiPlt',
      btnOptions: [
        {
          label: 'Edit',
          icon: <RixEngineFont type="edit" />,
          onClick: handleEdit,
        },
      ],
    });

    return [...KwaColumnOptions, operateColumn];
  }, []);

  useEffect(() => {
    const options = KwaSearchOption.map((v) => ({ ...v }));
    let tenantOptions = Array.isArray(tenantList)
      ? tenantList.map((v) => ({
          label: `${v.tnt_name}(${v.tnt_id})`,
          value: v.tnt_id,
        }))
      : [];
    let demandOptions = Array.isArray(allDemandList)
      ? allDemandList.map((v) => ({
          label: `${v.buyer_name}(${v.buyer_id})`,
          value: v.buyer_id,
        }))
      : [];
    const tIndex = options.findIndex((v) => v.key === 'tnt_id');
    const dIndex = options.findIndex((v) => v.key === 'buyer_id');
    if (tIndex !== -1) {
      options[tIndex].options = tenantOptions;
    }
    if (dIndex !== -1) {
      options[dIndex].options = demandOptions;
    }
    setSearchOptions(options);
  }, [tenantList, allDemandList]);

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };
  return (
    <>
      <FrontTable<ConfigAPI.KwaiPlacementItem>
        searchOptions={searchOptions}
        loading={loading || tntLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Kwai Placement',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            access: 'AddKwaiPlt',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        defaultTab={'kwai'}
        tabOptions={tabOptions}
        onTabChange={onTabChange}
        initialValues={defaultParams}
      />
      <AddKwaModel
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        tenantList={tenantList || []}
        allDemandList={allDemandList || []}
      />
    </>
  );
};

export default KwaiPlacementPage;
