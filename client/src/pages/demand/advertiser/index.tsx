/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 12:19:11
 * @Description:
 */
import InfoBar from '@/components/InfoBar';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import { TopBarSearchItem } from '@/components/TopBar';
import { DemandAndSupplyStatusMap, RegionTypeDesc } from '@/constants';
import {
  ADFormat,
  DemandBreadOptions,
  DemandSearchOption,
} from '@/constants/demand';
import { DemandColumns } from '@/constants/demand/demand-columns';
import { DemandInfoTabs } from '@/constants/demand/info';
import { getDemandAuth } from '@/services/demand';
import { fetchData } from '@/utils';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import AddDemandModel from '../components/AddDemandDrawer';
import styles from './index.less';

const Page: React.FC = () => {
  const { demandList, reload, loading } = useModel('useDemandListWithTesting');
  const { tenantList, reload: reloadTenant } = useModel('useTenantList');
  const { dataSource: endpointList, reload: reloadEndpoint } = useModel(
    'useDemandEndpointList',
  );
  const { buyerPartnerList: partnerList, reload: reloadPartner } =
    useModel('usePartnerList');
  const { dataSource: integrationTypeList, reload: reloadIntegrationType } =
    useModel('useBuyerIntegrationTypeList');

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [demand, setCurrentDemand] = useState<
    DemandAPI.DemandListItem | undefined
  >(undefined);
  const [searchOptions, setSearchOptions] = useState(DemandSearchOption);
  const [dataSource, setDataSource] = useState<DemandAPI.DemandListItem[]>([]);

  const [currentRow, setCurrentRow] = useState<
    DemandAPI.DemandListItem | undefined
  >(undefined);
  const [activeKey, setActiveKey] = useState('basic');
  const [demandInfoTabs, setDemandInfoTabs] = useState(DemandInfoTabs);
  const [infoLoading, setInfoLoading] = useState(false);

  const handleEditDemand = (params: DemandAPI.DemandListItem) => {
    setCurrentDemand(params);
    setIsEdit(true);
    setVisible(true);
  };

  const getDemandAuthList = () => {
    fetchData({
      setLoading: setInfoLoading,
      request: getDemandAuth,
      params: { buyer_id: currentRow?.buyer_id, tnt_id: currentRow?.tnt_id },
      onSuccess: (data: any[]) => {
        if (data) {
          const index = demandInfoTabs.findIndex(
            (item) => item.key === 'authorization',
          );
          const tabs = [...demandInfoTabs];
          if (index !== -1) {
            tabs[index].tableData = data.map((item) => {
              return {
                auth_seller_id: item.seller_id,
                auth_seller_name: item.seller_name,
                integration_type_desc: integrationTypeList.find(
                  (i) => i.id === item.integration_type,
                )?.itg_name,
              };
            });
          }
          setDemandInfoTabs(tabs);
        }
      },
    });
  };

  useEffect(() => {
    if (!demandList) {
      reload();
    }
    reloadEndpoint();
    if (!tenantList) {
      reloadTenant();
    }
    if (!integrationTypeList) {
      reloadIntegrationType();
    }

    if (!partnerList?.length) {
      reloadPartner();
    }
  }, []);

  useEffect(() => {
    if (Array.isArray(demandList)) {
      setDataSource(demandList);
    }
  }, [demandList]);

  useEffect(() => {
    if (currentRow) {
      const tabs = [...DemandInfoTabs];
      const baseIndex = tabs.findIndex(
        (item) => item.key === 'basic' && item.titleIcon,
      );
      if (baseIndex !== -1) {
        if (currentRow?.status === DemandAndSupplyStatusMap.Testing) {
          tabs[baseIndex].titleIcon!.icon = '';
        } else {
          tabs[baseIndex].titleIcon!.icon = (
            <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />
          );
          tabs[baseIndex].titleIcon!.onClick = (row) => {
            setCurrentRow(undefined);
            handleEditDemand(row!);
          };
        }
      }
      setDemandInfoTabs(tabs);
      if (activeKey === 'authorization') {
        getDemandAuthList();
      }
    }
  }, [currentRow]);

  useEffect(() => {
    if (!tenantList || !integrationTypeList || !demandList) return;
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (integrationTypeList && integrationTypeList.length) {
      const tmp = integrationTypeList.map((item) => {
        return {
          label: item.itg_name,
          value: item.id,
        };
      });
      const index = options.findIndex(
        (item) => item.key === 'integration_type',
      );
      if (index !== -1) {
        options[index].options = tmp;
      }
    }
    if (demandList && tenantList) {
      const sOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id,
        };
      });
      const tOptions = tenantList.map((item: TenantAPI.TenantListItem) => {
        return {
          label: `${item.tnt_name}(${item.tnt_id})`,
          value: item.tnt_id,
        };
      });
      const sIndex = options.findIndex((item) => item.key === 'buyer_id');
      const tIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
      if (tIndex !== -1) {
        options[tIndex].options = tOptions;
      }
    }
    setSearchOptions(options);
  }, [integrationTypeList, demandList, tenantList]);

  const handleClose = () => {
    setVisible(false);
  };

  const columns = useMemo(() => {
    const operateColumn = genOperateColumn<DemandAPI.DemandListItem>({
      width: 100,
      btnOptions: [
        {
          label: 'Edit',
          onClick: handleEditDemand,
          icon: <RixEngineFont type="edit" />,
          handleDisabled: (params) =>
            params.status === DemandAndSupplyStatusMap.Testing,
        },
      ],
    });
    return [...DemandColumns, operateColumn];
  }, []);

  const normalEmptyRender = () => <span></span>;

  const handleClickRow = (record: DemandAPI.DemandListItem) => {
    if (record.buyer_id === currentRow?.buyer_id) {
      setCurrentRow(undefined);
    } else {
      const columnsKeys = [
        'connect_timeout',
        'socket_timeout',
        'server_region',
        'gzip',
        'url',
        'ad_format',
      ];

      const endpoint = endpointList?.filter(
        (item) => item.buyer_id === record.buyer_id,
      );

      const tmp: any = { ...record };
      if (Array.isArray(endpoint)) {
        endpoint.forEach((item) => {
          columnsKeys.forEach((key) => {
            tmp[
              `${RegionTypeDesc[item.server_region]}_${
                ADFormat[item.ad_format]
              }_${key}`
            ] = item[key as keyof DemandAPI.EndpointItem];
          });
        });

        setCurrentRow(tmp);
      }
    }
  };

  const handleInfoTabChange = (activeKey: string) => {
    setActiveKey(activeKey);
    if (activeKey === 'authorization') {
      const index = demandInfoTabs.findIndex(
        (item) => item.key === 'authorization',
      );
      if (index !== -1) {
        getDemandAuthList();
      }
    }
  };
  const handleSearchValueChange = (changeValue: any) => {
    if (changeValue.tnt_id && Array.isArray(demandList)) {
      const dOptions: any[] = demandList
        .filter(
          (v: ConfigAPI.DemandListItem) =>
            !changeValue.tnt_id.length || changeValue.tnt_id.includes(v.tnt_id),
        )
        .map((item: ConfigAPI.DemandListItem) => {
          return {
            label: `${item.buyer_name}(${item.buyer_id})`,
            value: item.buyer_id,
          };
        });
      const dIndex = searchOptions.findIndex((item) => item.key === 'buyer_id');
      if (dIndex !== -1) {
        searchOptions[dIndex].options = dOptions;
        setSearchOptions([...searchOptions]);
      }
    }
  };

  return (
    <PageContainer flexDirection="column" options={DemandBreadOptions}>
      <FrontTable<DemandAPI.DemandListItem>
        pageTitle="Advertiser List"
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'buyer_id'}
        request={reload}
        labelWidth={120}
        isFold
        scroll={{ x: 1000, y: 'calc(100vh - 220px)' }}
        emptyRender={dataSource?.length ? normalEmptyRender : undefined}
        initialValues={{
          status: [
            DemandAndSupplyStatusMap.Active,
            DemandAndSupplyStatusMap.Testing,
          ],
        }}
        onRow={(record) => {
          return {
            onClick: () => handleClickRow(record),
          };
        }}
        rowClassName={(record) => {
          return record.buyer_id === currentRow?.buyer_id
            ? styles['row-selected']
            : '';
        }}
        handleSearchValueChange={handleSearchValueChange}
        allowSaveSearch
      />
      <AddDemandModel
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadDemand={reload}
        demand={demand}
        partnerList={partnerList}
        integrationTypeList={integrationTypeList}
      />
      <InfoBar<DemandAPI.DemandListItem, DemandAPI.EndpointItem>
        loading={infoLoading}
        dataSource={currentRow}
        title={currentRow?.buyer_name || ''}
        tabs={demandInfoTabs}
        width={700}
        handleClose={() => setCurrentRow(undefined)}
        defaultActiveKey={activeKey}
        handleTabChange={handleInfoTabChange}
      />
    </PageContainer>
  );
};

export default Page;
