/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 18:34:14
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:01:33
 * @Description:
 */

import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import { RoleColumns, RoleSearchOption } from '@/constants/admin-setting/role';
import { addAdminRole, updateAdminRole } from '@/services/api';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useMemo, useState } from 'react';
import EditRoleModel from '../components/EditRoleModel';

type RoleListProps = {
  dataSource: AdminSettingAPI.RoleItem[];
  loading: boolean;
  reload: () => void;
};
const RolePage: React.FC<RoleListProps> = ({ dataSource, loading, reload }) => {
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<AdminSettingAPI.RoleItem>();
  const [searchOptions, setSearchOptions] = useState(RoleSearchOption);

  useEffect(() => {
    if (dataSource?.length) {
      const options = dataSource.map((v) => ({
        label: v.role_name,
        value: v.id,
      }));
      const arr = RoleSearchOption.map((v) => ({ ...v }));
      const index = arr.findIndex((v) => v.key === 'id');
      if (index !== -1) {
        arr[index].options = options;
      }
      setSearchOptions(arr);
    }
  }, [dataSource]);

  const handleClose = () => {
    setVisible(false);
  };

  const handleSave = () => {
    setVisible(false);
    reload();
  };

  // 批量操作使用的selectedRows
  const handleClickBtn = () => {
    setVisible(true);
    setIsEdit(false);
    setEditItem(undefined);
  };

  const columns = useMemo(() => {
    const handleEdit = (row: AdminSettingAPI.RoleItem) => {
      setEditItem(row);
      setIsEdit(true);
      setVisible(true);
    };

    return [
      ...RoleColumns,
      genOperateColumn<AdminSettingAPI.RoleItem>({
        title: '操作',
        width: 90,
        access: 'EditAdminRole',
        btnOptions: [
          {
            label: '编辑',
            icon: <RixEngineFont type="edit" />,
            onClick: handleEdit,
          },
        ],
      }),
    ];
  }, []);

  return (
    <>
      <FrontTable<AdminSettingAPI.RoleItem>
        pageTitle="角色列表"
        searchOptions={searchOptions}
        loading={loading}
        request={reload}
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        labelWidth={50}
        scroll={{ x: 1000, y: 'auto' }}
        isBtnTable
        isFold
        btnOptions={[
          {
            label: '新增角色',
            type: 'primary',
            size: 'small',
            icon: <PlusOutlined />,
            onClick: handleClickBtn,
            access: 'AddAdminRole',
          },
        ]}
      />
      <EditRoleModel
        visible={visible}
        isEdit={isEdit}
        onSave={handleSave}
        onClose={handleClose}
        request={isEdit ? updateAdminRole : addAdminRole}
        item={editItem}
      />
    </>
  );
};

export default RolePage;
