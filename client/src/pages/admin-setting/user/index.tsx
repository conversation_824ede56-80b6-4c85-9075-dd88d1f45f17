/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-31 22:07:18
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-27 10:57:41
 * @Description:
 */

import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import CommonPage, { ChildrenProps } from '@/components/Table/CommonFrontPage';
import {
  UserBreadOptions,
  UserBtnOptions,
  UserColumns,
  UserSearchOption,
} from '@/constants/admin-setting/user';
import { addAdminUser, updateAdminUser } from '@/services/api';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import EditUserModel from '../components/EditUserModel';
import ResetPwdModal from '../components/ResetPwdModal';

const UserPermission: React.FC = () => {
  const {
    allUserList: userList = [],
    reload,
    loading,
  } = useModel('useAdminUserList');
  const {
    dataSource: roleList,
    reload: reloadRole,
    loading: roleLoading,
  } = useModel('useAdminRoleList');
  const [visible, setVisible] = useState(false);
  const [editItem, setEditItem] = useState<UserAPI.UserListItem>();
  const [searchOptions, setSearchOptions] = useState(UserSearchOption);

  useEffect(() => {
    if (!roleList || !roleList.length) {
      reloadRole();
    }
  }, []);

  useEffect(() => {
    const arr = UserSearchOption.map((v) =>
      v.key === 'admin_id'
        ? {
            ...v,
            options: userList ? userList.map((item) => ({
              label: item.account_name,
              value: item.admin_id,
            })) : []
          }
        : v
    );
    if (Array.isArray(roleList) && roleList.length) {
      const index = arr.findIndex((v) => v.key === 'role_id');
      if (index !== -1) {
        arr[index].options = roleList.map((v) => ({
          label: v.role_name,
          value: v.id,
        }));
      }
    }
    setSearchOptions(arr);
  }, [userList, roleList]);

  const columns = useMemo(() => {
    const handleResetPassword = (params: UserAPI.UserListItem) => {
      setVisible(true);
      setEditItem(params);
    };

    return [
      ...UserColumns,
      genOperateColumn<UserAPI.UserListItem>({
        title: '操作',
        width: 170,
        access: ['EditAdminUser', 'ResetAdminPassword'],
        btnOptions: [
          {
            label: '重置密码',
            icon: <RixEngineFont type="rix-reset" />,
            onClick: handleResetPassword,
          },
        ],
      }),
    ];
  }, []);

  return (
    <PageContainer options={UserBreadOptions}>
      <CommonPage<UserAPI.UserListItem>
        pageTitle="账号列表"
        searchOptions={searchOptions}
        loading={loading || roleLoading}
        reload={reload}
        dataSource={userList}
        rowKey="admin_id"
        btnOptions={UserBtnOptions}
        columns={columns}
        labelWidth={50}
        editAccess="EditAdminUser"
      >
        {
          // @ts-ignore
          (props: ChildrenProps) => {
            return (
              <EditUserModel
                request={props.isEdit ? updateAdminUser : addAdminUser}
                roleList={roleList || []}
                {...props}
              />
            );
          }
        }
      </CommonPage>
      <ResetPwdModal
        visible={visible}
        editItem={editItem}
        onClose={() => setVisible(false)}
        onSave={() => setVisible(false)}
      />
    </PageContainer>
  );
};

export default UserPermission;
