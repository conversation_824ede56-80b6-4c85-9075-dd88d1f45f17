import { describe, it, expect, vi } from 'vitest';
import {
  formatTime,
  formatUTC8Time,
  objectFlip,
  isValidBundle,
  isNumber,
  handleFilterSelect,
  deepClone,
  formatMoney,
  isArrSame,
  isObjectValueEqual,
  removeZWSpace,
  formatNumberToUnit,
  validateDomain,
  generateMap,
} from '../index';

describe('Utils Functions', () => {
  describe('formatTime', () => {
    it('should format date to UTC time string with default pattern', () => {
      const date = new Date('2023-12-25T10:30:45.000Z');
      const result = formatTime(date);
      expect(result).toBe('2023-12-25 10:30:45');
    });

    it('should format date with custom pattern', () => {
      const date = new Date('2023-12-25T10:30:45.000Z');
      const result = formatTime(date, 'yyyy/MM/dd hh:mm');
      expect(result).toBe('2023/12/25 10:30');
    });

    it('should handle string date input', () => {
      const result = formatTime('2023-12-25T10:30:45.000Z');
      expect(result).toBe('2023-12-25 10:30:45');
    });
  });

  describe('formatUTC8Time', () => {
    it('should format date to local time string', () => {
      const date = new Date('2023-12-25T10:30:45.000Z');
      const result = formatUTC8Time(date);
      // Note: This will depend on the system timezone, so we just check format
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
    });
  });

  describe('objectFlip', () => {
    it('should flip object keys and values', () => {
      const input = { a: 1, b: 2, c: 'test' };
      const result = objectFlip(input);
      expect(result).toEqual({ '1': 'a', '2': 'b', 'test': 'c' });
    });

    it('should handle empty object', () => {
      const result = objectFlip({});
      expect(result).toEqual({});
    });
  });

  describe('isValidBundle', () => {
    it('should validate iOS bundle (numeric)', () => {
      expect(isValidBundle('*********')).toBe(true);
      expect(isValidBundle('0')).toBe(true);
    });

    it('should validate Android bundle (package name format)', () => {
      expect(isValidBundle('com.example.app')).toBe(true);
      expect(isValidBundle('com.company.my-app')).toBe(true);
      expect(isValidBundle('com.test.app_name')).toBe(true);
    });

    it('should reject invalid bundles', () => {
      expect(isValidBundle('invalid')).toBe(false);
      expect(isValidBundle('123abc')).toBe(false);
      expect(isValidBundle('.com.example')).toBe(false);
      expect(isValidBundle('')).toBe(false);
    });
  });

  describe('isNumber', () => {
    it('should identify numbers correctly', () => {
      expect(isNumber(123)).toBe(true);
      expect(isNumber(0)).toBe(true);
      expect(isNumber(-456)).toBe(true);
      expect(isNumber(3.14)).toBe(true);
    });
  });

  describe('handleFilterSelect', () => {
    it('should filter options by input (case insensitive)', () => {
      const option1 = { children: 'Test Option' };
      const option2 = { label: 'Another Test' };
      
      expect(handleFilterSelect('test', option1)).toBe(true);
      expect(handleFilterSelect('TEST', option1)).toBe(true);
      expect(handleFilterSelect('another', option2)).toBe(true);
      expect(handleFilterSelect('xyz', option1)).toBe(false);
    });

    it('should handle array children', () => {
      const option = { children: ['Test', ' ', 'Option'] };
      expect(handleFilterSelect('test', option)).toBe(true);
      expect(handleFilterSelect('option', option)).toBe(true);
    });
  });

  describe('deepClone', () => {
    it('should clone primitive values', () => {
      expect(deepClone(123)).toBe(123);
      expect(deepClone('test')).toBe('test');
      expect(deepClone(null)).toBe(null);
      expect(deepClone(undefined)).toBe(undefined);
    });

    it('should clone objects', () => {
      const obj = { a: 1, b: { c: 2 } };
      const cloned = deepClone(obj);
      
      expect(cloned).toEqual(obj);
      expect(cloned).not.toBe(obj);
      expect(cloned.b).not.toBe(obj.b);
    });

    it('should clone arrays', () => {
      const arr = [1, [2, 3], { a: 4 }];
      const cloned = deepClone(arr);
      
      expect(cloned).toEqual(arr);
      expect(cloned).not.toBe(arr);
      expect(cloned[1]).not.toBe(arr[1]);
      expect(cloned[2]).not.toBe(arr[2]);
    });

    it('should handle functions', () => {
      const fn = () => 'test';
      const cloned = deepClone(fn);
      expect(cloned).toBe(fn);
    });
  });

  describe('formatMoney', () => {
    it('should format numbers with commas', () => {
      expect(formatMoney(1000)).toBe('1,000');
      expect(formatMoney(1234567)).toBe('1,234,567');
      expect(formatMoney(123)).toBe('123');
    });
  });

  describe('isArrSame', () => {
    it('should return true for same arrays', () => {
      expect(isArrSame([1, 2, 3], [3, 2, 1])).toBe(true);
      expect(isArrSame(['a', 'b'], ['b', 'a'])).toBe(true);
    });

    it('should return false for different arrays', () => {
      expect(isArrSame([1, 2, 3], [1, 2, 4])).toBe(false);
      expect(isArrSame([1, 2], [1, 2, 3])).toBe(false);
    });
  });

  describe('isObjectValueEqual', () => {
    it('should return true for equal objects', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { a: 1, b: 2 };
      expect(isObjectValueEqual(obj1, obj2)).toBe(true);
    });

    it('should return false for different objects', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { a: 1, b: 3 };
      expect(isObjectValueEqual(obj1, obj2)).toBe(false);
    });

    it('should return false for objects with different properties', () => {
      const obj1 = { a: 1, b: 2 };
      const obj2 = { a: 1, c: 2 };
      expect(isObjectValueEqual(obj1, obj2)).toBe(false);
    });
  });

  describe('removeZWSpace', () => {
    it('should remove zero-width spaces', () => {
      const input = 'test\u200bstring\u2028with\u2029spaces';
      const result = removeZWSpace(input);
      expect(result).toBe('teststringwithspaces');
    });

    it('should handle empty string', () => {
      expect(removeZWSpace('')).toBe('');
    });

    it('should handle null/undefined', () => {
      expect(removeZWSpace(null as any)).toBe(null);
      expect(removeZWSpace(undefined as any)).toBe(undefined);
    });
  });

  describe('formatNumberToUnit', () => {
    it('should format billions', () => {
      expect(formatNumberToUnit(1500000000)).toBe('1.50B');
      expect(formatNumberToUnit(2000000000, 2, 1)).toBe('2.0B');
    });

    it('should format millions', () => {
      expect(formatNumberToUnit(1500000)).toBe('1.50M');
      expect(formatNumberToUnit(2000000, 2, 1)).toBe('2.0M');
    });

    it('should format thousands', () => {
      expect(formatNumberToUnit(1500)).toBe('1.50K');
      expect(formatNumberToUnit(2000, 2, 1)).toBe('2.0K');
    });

    it('should format small numbers', () => {
      expect(formatNumberToUnit(999)).toBe('999.00');
      expect(formatNumberToUnit(123, 1)).toBe('123.0');
    });
  });

  describe('validateDomain', () => {
    it('should resolve for valid domains', async () => {
      await expect(validateDomain('example.com')).resolves.toBeUndefined();
      await expect(validateDomain('sub.example.com')).resolves.toBeUndefined();
      await expect(validateDomain('test-site.co.uk')).resolves.toBeUndefined();
    });

    it('should reject invalid domains', async () => {
      await expect(validateDomain('invalid')).rejects.toMatch('请输入正确的域名');
      await expect(validateDomain('http://example.com')).rejects.toMatch('请输入正确的域名');
      await expect(validateDomain('.example.com')).rejects.toMatch('请输入正确的域名');
    });

    it('should resolve for empty value', async () => {
      await expect(validateDomain('')).resolves.toBeUndefined();
    });
  });

  describe('generateMap', () => {
    it('should generate key-value map from array', () => {
      const data = [
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' },
      ];
      const result = generateMap(data, 'id', 'name');
      expect(result).toEqual({ '1': 'John', '2': 'Jane' });
    });

    it('should handle empty array', () => {
      const result = generateMap([], 'id', 'name');
      expect(result).toEqual({});
    });
  });
});
