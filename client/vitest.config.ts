/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    // 测试环境
    environment: 'jsdom',
  },
  typecheck: {
    tsconfig: './tsconfig.vitest.json',
    
    // 全局设置
    globals: true,
    
    // 设置文件
    setupFiles: ['./src/test/setup.ts'],
    
    // 测试文件匹配模式
    include: [
      'src/**/*.{test,spec}.{js,ts,jsx,tsx}',
      'tests/**/*.{test,spec}.{js,ts,jsx,tsx}'
    ],
    
    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      '.umi',
      '.umi-production',
      '.umi-test'
    ],
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.{js,ts}',
        'src/.umi/**',
        'src/loading.tsx',
        'src/app.tsx',
        'config/**',
        'public/**',
        'dist/**',
        '.umi/**',
        '.umi-production/**',
        '.umi-test/**'
      ],
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 50,
          functions: 50,
          lines: 50,
          statements: 50
        }
      }
    },
    
    // 测试超时时间
    testTimeout: 10000,
    
    // 钩子超时时间
    hookTimeout: 10000,
    
    // 并发运行
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false
      }
    }
  },
  
  // 解析配置
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@@': path.resolve(__dirname, './src/.umi')
    }
  },
  
  // 定义全局变量
  define: {
    'process.env.NODE_ENV': '"test"',
    'process.env.UMI_ENV': '"test"'
  },
  
  // CSS处理配置
  css: {
    // 使用 Vitest 的 CSS 模块配置
    // 在 Vitest 3.x 中，可以使用以下方式配置 CSS 模块
    modules: {
      // classNameStrategy 配置类名生成策略
      // 'stable' - 使用一致的哈希（默认）
      // 'scoped' - 尊重自定义生成器
      // 'non-scoped' - 避免哈希，保持原始类名
      // @ts-ignore - Vitest 3.x 支持此选项，但 TypeScript 类型定义可能未更新
      classNameStrategy: 'non-scoped'
    }
  },
  
  // 添加esbuild配置来处理CSS imports
  esbuild: {
    target: 'node18'
  },
  
  // 添加自定义插件来处理CSS文件
  plugins: [
    {
      name: 'css-mock',
      enforce: 'pre',
      load(id) {
        // Only match .css/.less/.scss, including optional query strings
        if (!/\.(css|less|scss)(\?.*)?$/.test(id)) return

        const query = id.split('?')[1] || ''
        // Let Vite handle CSS modules or special loaders (inline, url, etc.)
        if (/(^|&)module(&|=|$)|(^|&)(inline|url)(=|&|$)/.test(query)) return null

        // Stub out side-effect style imports
        return 'export default {};'
      }
    }
  ]
});
