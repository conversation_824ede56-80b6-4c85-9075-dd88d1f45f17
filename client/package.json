{"private": true, "scripts": {"dev": "cross-env UMI_ENV=dev max dev", "build": "cross-env UMI_ENV=prod max build", "builddeploy": "cross-env UMI_ENV=prod max build && rm -rf ../server/webroot/* && cp -Rf dist/. ../server/webroot && rm -rf dist*", "format": "prettier --cache --write .", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "start": "npm run dev", "lint": "eslint --fix --ext .ts,.tsx src/", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:coverage:watch": "vitest --coverage --watch"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/charts": "^1.4.3", "@ant-design/icons": "^4.7.0", "@ant-design/pro-card": "^1.20.17", "@ant-design/pro-components": "^1.1.3", "@ant-design/pro-layout": "^6.38.18", "@rixfe/rix-tools": "^1.5.0", "@umijs/max": "^4.0.13", "ahooks": "^3.7.0", "antd": "^4.22.7", "click-to-react-component": "^1.1.0", "js-base64": "^3.7.4", "json2csv": "^5.0.7", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "query-string": "^7.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-svg": "^15.1.9"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dom-speech-recognition": "^0.0.4", "@types/json2csv": "^5.0.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitest/coverage-v8": "^3.2.4", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "jsdom": "^26.1.0", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^2", "prettier-plugin-packagejson": "^2", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3", "typescript": "^4.1.2", "vitest": "^3.2.4"}}